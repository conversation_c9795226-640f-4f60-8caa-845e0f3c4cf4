-- Quick test script for vehicles loading
-- Run this in server console: exec test_vehicles.lua

print("=== VEHICLES TEST ===")

-- Test 1: Check if QBX.Shared.Vehicles exists
if QBX and QBX.Shared and QBX.Shared.Vehicles then
    local count = 0
    for _ in pairs(QBX.Shared.Vehicles) do count = count + 1 end
    print("✓ QBX.Shared.Vehicles exists with " .. count .. " vehicles")
else
    print("✗ QBX.Shared.Vehicles is nil or empty")
end

-- Test 2: Check if QBX.Shared.VehicleHashes exists
if QBX and QBX.Shared and QBX.Shared.VehicleHashes then
    local count = 0
    for _ in pairs(QBX.Shared.VehicleHashes) do count = count + 1 end
    print("✓ QBX.Shared.VehicleHashes exists with " .. count .. " vehicles")
else
    print("✗ QBX.Shared.VehicleHashes is nil or empty")
end

-- Test 3: Test GetVehiclesByName export
local success, result = pcall(function()
    return exports.qbx_core:GetVehiclesByName()
end)

if success then
    local count = 0
    for _ in pairs(result) do count = count + 1 end
    print("✓ GetVehiclesByName export works with " .. count .. " vehicles")
else
    print("✗ GetVehiclesByName export failed: " .. tostring(result))
end

-- Test 4: Test specific vehicle
local success2, result2 = pcall(function()
    return exports.qbx_core:GetVehiclesByName('adder')
end)

if success2 and result2 then
    print("✓ GetVehiclesByName('adder') works: " .. (result2.name or "no name"))
else
    print("✗ GetVehiclesByName('adder') failed or returned nil")
end

print("=== END TEST ===")
