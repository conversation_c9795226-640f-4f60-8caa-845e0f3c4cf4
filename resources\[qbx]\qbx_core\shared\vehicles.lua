-- Vehicles data loader from database
-- This file loads vehicle data from core_vehicles table instead of static data
-- Maintains compatibility with existing scripts that expect vehicles.lua

local vehicles = {}

-- Check if we're on server side (MySQL is only available on server)
if IsDuplicityVersion() then
    -- Server side - load from database
    print('[qbx_core] Attempting to load vehicles from database...')

    local success, result = pcall(MySQL.Sync.fetchAll, 'SELECT * FROM core_vehicles', {})

    if not success then
        print('[qbx_core] ERROR: Failed to query database: ' .. tostring(result))
        return vehicles
    end

    if not result then
        print('[qbx_core] WARNING: Database query returned nil')
        return vehicles
    end

    print(('[qbx_core] Database query returned %d rows'):format(#result))

    if result and #result > 0 then
        for _, vehicle in pairs(result) do
            vehicles[vehicle.id] = {
                name = vehicle.name,
                brand = vehicle.brand,
                model = vehicle.model,
                price = vehicle.price,
                category = vehicle.category,
                type = vehicle.type,
                hash = joaat(vehicle.hash), -- Convert hash string to joaat hash
            }
        end
    else
        print('[qbx_core] WARNING: No vehicles found in database')
    end

    local count = 0
    for _ in pairs(vehicles) do count = count + 1 end
    print(('[qbx_core] Loaded %d vehicles from database'):format(count))
else
    -- Client side - vehicles will be empty initially, populated later via events
    -- This is just a placeholder for compatibility
end

---@type table<string, Vehicle>
return vehicles
