-- Vehicles data loader from database
-- This file loads vehicle data from core_vehicles table instead of static data

local vehicles = {}

-- Check if we're on server side (MySQL is only available on server)
if IsDuplicityVersion() then
    -- Server side - load from database
    local result = MySQL.Sync.fetchAll('SELECT * FROM core_vehicles', {})

    if result then
        for _, vehicle in pairs(result) do
            vehicles[vehicle.id] = {
                name = vehicle.name,
                brand = vehicle.brand,
                model = vehicle.model,
                price = vehicle.price,
                category = vehicle.category,
                type = vehicle.type,
                hash = joaat(vehicle.hash), -- Convert hash string to joaat hash
            }
        end
    end

    local count = 0
    for _ in pairs(vehicles) do count = count + 1 end
    print(('[qbx_core] Loaded %d vehicles from database'):format(count))
else
    -- Client side - vehicles will be loaded via shared/main.lua from server
    -- This is just a placeholder, actual loading happens in shared/main.lua
end

---@type table<string, Vehicle>
return vehicles
