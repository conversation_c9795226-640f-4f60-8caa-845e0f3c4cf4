-- Vehicles proxy - redirects to database-loaded vehicles
-- This maintains compatibility with scripts that expect vehicles.lua to exist
-- while actually loading data from core_vehicles database table

-- On server side, return the loaded vehicles from VehiclesLoader
-- On client side, return the vehicles received from server
if IsDuplicityVersion() then
    -- Server side - get vehicles from VehiclesLoader if available
    local success, VehiclesLoader = pcall(require, 'shared.vehicles_loader')
    if success and VehiclesLoader and VehiclesLoader.Vehicles then
        return VehiclesLoader.Vehicles
    else
        -- Fallback: load directly from database if VehiclesLoader not available yet
        local vehicles = {}
        local result = MySQL.Sync.fetchAll('SELECT * FROM core_vehicles', {})
        
        if result then
            for _, vehicle in pairs(result) do
                vehicles[vehicle.id] = {
                    name = vehicle.name,
                    brand = vehicle.brand,
                    model = vehicle.model,
                    price = vehicle.price,
                    category = vehicle.category,
                    type = vehicle.type,
                    hash = joaat(vehicle.hash),
                }
            end
        end
        
        return vehicles
    end
else
    -- Client side - get vehicles from QBX.Shared if available
    if QBX and QBX.Shared and QBX.Shared.Vehicles then
        return QBX.Shared.Vehicles
    else
        -- Return empty table if not loaded yet
        return {}
    end
end
