---@type table<string, Vehicle>
return {
    --Civilian
    sunrise1 = {
        name = 'Sunrise',
        brand = '<PERSON><PERSON><PERSON>',
        model = 'sunrise1',
        price = 86065,
        category = 'sedans',
        type = 'automobile',
        hash = `sunrise1`,
    },
    rrelegyextreme = {
        name = 'RH-8 Bodykit',
        brand = 'Elegy',
        model = 'rrelegyextreme',
        price = 86065,
        category = 'sports',
        type = 'automobile',
        hash = `rrelegyextreme`,
    },
    rt3000varis = {
        name = 'RT3000 Varis',
        brand = 'Dinka',
        model = 'rt3000varis',
        price = 86065,
        category = 'sports',
        type = 'automobile',
        hash = `rt3000varis`,
    },
    rhinesed = {
        name = 'Rhinehart Sedan',
        brand = 'Übermacht',
        model = 'rhinesed',
        price = 86065,
        category = 'sedans',
        type = 'automobile',
        hash = `rhinesed`,
    },
    --EMS
    ambuimp = {
        name = 'Ambulance',
        brand = 'Ambulance',
        model = 'ambuimp',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `ambuimp`,
    },
    emsscout = {
        name = 'Scout EMS',
        brand = 'Vapid',
        model = 'emsscout',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `emsscout`,
    },
    --Police
    pvsjv = {
        name = 'Scout PD',
        brand = 'Vapid',
        model = 'pvsjv',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `pvsjv`,
    },
    pbbjv = {
        name = 'STX',
        brand = 'Bravo',
        model = 'pbbjv',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `pbbjv`,
    },
    pbgjv = {
        name = 'Hellfire PD',
        brand = 'Bravado',
        model = 'pbgjv',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `pbgjv`,
    },
    pvtjv = {
        name = 'Torrence',
        brand = 'Vapid',
        model = 'pvtjv',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `pvtjv`,
    },
    expolalamo = {
        name = 'Alamo',
        brand = 'Declasse',
        model = 'expolalamo',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `expolalamo`,
    },
    sent5pd = {
        name = 'Sentinel GTS',
        brand = 'Übermacht',
        model = 'sent5pd',
        price = 86065,
        category = 'emergency',
        type = 'automobile',
        hash = `sent5pd`,
    },
    --vanilla
    adder = {
        name = 'Adder',
        brand = 'Truffade',
        model = 'adder',
        price = 86065,
        category = 'super',
        type = 'automobile',
        hash = `adder`,
    },
    airbus = {
        name = 'Airport Bus',
        brand = '',
        model = 'airbus',
        price = 42321,
        category = 'service',
        type = 'automobile',
        hash = `airbus`,
    },
    airtug = {
        name = 'Airtug',
        brand = 'HVY',
        model = 'airtug',
        price = 18786,
        category = 'utility',
        type = 'automobile',
        hash = `airtug`,
    },
    akula = {
        name = 'Akula',
        brand = 'Buckingham',
        model = 'akula',
        price = 6879704,
        category = 'helicopters',
        type = 'heli',
        hash = `akula`,
    },
    akuma = {
        name = 'Akuma',
        brand = 'Dinka',
        model = 'akuma',
        price = 25356,
        category = 'motorcycles',
        type = 'bike',
        hash = `akuma`,
    },
    aleutian = {
        name = 'Aleutian',
        brand = 'Vapid',
        model = 'aleutian',
        price = 66169,
        category = 'suvs',
        type = 'automobile',
        hash = `aleutian`,
    },
    alkonost = {
        name = 'RO-86 Alkonost',
        brand = '',
        model = 'alkonost',
        price = 1933450,
        category = 'planes',
        type = 'plane',
        hash = `alkonost`,
    },
    alpha = {
        name = 'Alpha',
        brand = 'Albany',
        model = 'alpha',
        price = 82526,
        category = 'sports',
        type = 'automobile',
        hash = `alpha`,
    },
    alphaz1 = {
        name = 'Alpha-Z1',
        brand = 'Buckingham',
        model = 'alphaz1',
        price = 2587839,
        category = 'planes',
        type = 'plane',
        hash = `alphaz1`,
    },
    ambulance = {
        name = 'Ambulance',
        brand = 'Brute',
        model = 'ambulance',
        price = 66002,
        category = 'emergency',
        type = 'automobile',
        hash = `ambulance`,
    },
    annihilator = {
        name = 'Annihilator',
        brand = 'Western',
        model = 'annihilator',
        price = 5278061,
        category = 'helicopters',
        type = 'heli',
        hash = `annihilator`,
    },
    annihilator2 = {
        name = 'Annihilator Stealth',
        brand = 'Western',
        model = 'annihilator2',
        price = 5947464,
        category = 'helicopters',
        type = 'heli',
        hash = `annihilator2`,
    },
    apc = {
        name = 'APC',
        brand = 'HVY',
        model = 'apc',
        price = 38410,
        category = 'military',
        type = 'automobile',
        hash = `apc`,
    },
    ardent = {
        name = 'Ardent',
        brand = 'Ocelot',
        model = 'ardent',
        price = 79425,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `ardent`,
    },
    armytanker = {
        name = 'Army Trailer (Tanker)',
        brand = '',
        model = 'armytanker',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `armytanker`,
    },
    armytrailer = {
        name = 'Army Trailer',
        brand = '',
        model = 'armytrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `armytrailer`,
    },
    armytrailer2 = {
        name = 'Army Trailer (Civilian)',
        brand = '',
        model = 'armytrailer2',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `armytrailer2`,
    },
    asbo = {
        name = 'Asbo',
        brand = 'Maxwell',
        model = 'asbo',
        price = 63020,
        category = 'compacts',
        type = 'automobile',
        hash = `asbo`,
    },
    asea = {
        name = 'Asea',
        brand = 'Declasse',
        model = 'asea',
        price = 63374,
        category = 'sedans',
        type = 'automobile',
        hash = `asea`,
    },
    asea2 = {
        name = 'Asea (Snow)',
        brand = 'Declasse',
        model = 'asea2',
        price = 63374,
        category = 'sedans',
        type = 'automobile',
        hash = `asea2`,
    },
    asterope = {
        name = 'Asterope',
        brand = 'Karin',
        model = 'asterope',
        price = 64174,
        category = 'sedans',
        type = 'automobile',
        hash = `asterope`,
    },
    asterope2 = {
        name = 'Asterope GZ',
        brand = 'Karin',
        model = 'asterope2',
        price = 71306,
        category = 'sedans',
        type = 'automobile',
        hash = `asterope2`,
    },
    astron = {
        name = 'Astron',
        brand = 'Pfister',
        model = 'astron',
        price = 81719,
        category = 'suvs',
        type = 'automobile',
        hash = `astron`,
    },
    autarch = {
        name = 'Autarch',
        brand = 'Överflöd',
        model = 'autarch',
        price = 85798,
        category = 'super',
        type = 'automobile',
        hash = `autarch`,
    },
    avarus = {
        name = 'Avarus',
        brand = 'LCC',
        model = 'avarus',
        price = 23028,
        category = 'motorcycles',
        type = 'bike',
        hash = `avarus`,
    },
    avenger = {
        name = 'Avenger',
        brand = 'Mammoth',
        model = 'avenger',
        price = 1953425,
        category = 'planes',
        type = 'plane',
        hash = `avenger`,
    },
    avenger2 = {
        name = 'Avenger (Prop)',
        brand = 'Mammoth',
        model = 'avenger2',
        price = 1953425,
        category = 'planes',
        type = 'plane',
        hash = `avenger2`,
    },
    avenger3 = {
        name = 'Avenger (Upgraded)',
        brand = 'Mammoth',
        model = 'avenger3',
        price = 1953425,
        category = 'planes',
        type = 'plane',
        hash = `avenger3`,
    },
    avenger4 = {
        name = 'Avenger (Upgraded Prop)',
        brand = 'Mammoth',
        model = 'avenger4',
        price = 1953425,
        category = 'planes',
        type = 'plane',
        hash = `avenger4`,
    },
    avisa = {
        name = 'Avisa',
        brand = 'Kraken',
        model = 'avisa',
        price = 804616,
        category = 'boats',
        type = 'boat',
        hash = `avisa`,
    },
    bagger = {
        name = 'Bagger',
        brand = 'Western',
        model = 'bagger',
        price = 19934,
        category = 'motorcycles',
        type = 'bike',
        hash = `bagger`,
    },
    baletrailer = {
        name = 'Baletrailer',
        brand = '',
        model = 'baletrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `baletrailer`,
    },
    baller = {
        name = 'Baller',
        brand = 'Gallivanter',
        model = 'baller',
        price = 65628,
        category = 'suvs',
        type = 'automobile',
        hash = `baller`,
    },
    baller2 = {
        name = 'Baller',
        brand = 'Gallivanter',
        model = 'baller2',
        price = 74432,
        category = 'suvs',
        type = 'automobile',
        hash = `baller2`,
    },
    baller3 = {
        name = 'Baller LE',
        brand = 'Gallivanter',
        model = 'baller3',
        price = 74448,
        category = 'suvs',
        type = 'automobile',
        hash = `baller3`,
    },
    baller4 = {
        name = 'Baller LE LWB',
        brand = 'Gallivanter',
        model = 'baller4',
        price = 74384,
        category = 'suvs',
        type = 'automobile',
        hash = `baller4`,
    },
    baller5 = {
        name = ' Baller LE (Armored)',
        brand = 'Gallivanter',
        model = 'baller5',
        price = 74400,
        category = 'suvs',
        type = 'automobile',
        hash = `baller5`,
    },
    baller6 = {
        name = 'Baller LE LWB (Armored)',
        brand = 'Gallivanter',
        model = 'baller6',
        price = 74335,
        category = 'suvs',
        type = 'automobile',
        hash = `baller6`,
    },
    baller7 = {
        name = 'Baller ST',
        brand = 'Gallivanter',
        model = 'baller7',
        price = 77229,
        category = 'suvs',
        type = 'automobile',
        hash = `baller7`,
    },
    baller8 = {
        name = 'Baller ST-D',
        brand = 'Gallivanter',
        model = 'baller8',
        price = 77694,
        category = 'suvs',
        type = 'automobile',
        hash = `baller8`,
    },
    banshee = {
        name = 'Banshee',
        brand = 'Bravado',
        model = 'banshee',
        price = 79495,
        category = 'sports',
        type = 'automobile',
        hash = `banshee`,
    },
    banshee2 = {
        name = 'Banshee 900R',
        brand = 'Bravado',
        model = 'banshee2',
        price = 74887,
        category = 'super',
        type = 'automobile',
        hash = `banshee2`,
    },
    banshee3 = {
        name = 'Banshee GTS',
        brand = 'Bravado',
        model = 'banshee3',
        price = 74887,
        category = 'sports',
        type = 'automobile',
        hash = `banshee3`,
    },
    barracks = {
        name = 'Barracks',
        brand = 'HVY',
        model = 'barracks',
        price = 53283,
        category = 'military',
        type = 'automobile',
        hash = `barracks`,
    },
    barracks2 = {
        name = 'Barracks Semi',
        brand = 'HVY',
        model = 'barracks2',
        price = 60073,
        category = 'military',
        type = 'automobile',
        hash = `barracks2`,
    },
    barracks3 = {
        name = 'Barracks',
        brand = 'HVY',
        model = 'barracks3',
        price = 53283,
        category = 'military',
        type = 'automobile',
        hash = `barracks3`,
    },
    barrage = {
        name = 'Barrage',
        brand = 'HVY',
        model = 'barrage',
        price = 66580,
        category = 'military',
        type = 'automobile',
        hash = `barrage`,
    },
    bati = {
        name = 'Bati 801',
        brand = 'Pegassi',
        model = 'bati',
        price = 25838,
        category = 'motorcycles',
        type = 'bike',
        hash = `bati`,
    },
    bati2 = {
        name = 'Bati 801RR',
        brand = 'Pegassi',
        model = 'bati2',
        price = 25838,
        category = 'motorcycles',
        type = 'bike',
        hash = `bati2`,
    },
    benson = {
        name = 'Benson',
        brand = 'Vapid',
        model = 'benson',
        price = 60635,
        category = 'commercial',
        type = 'automobile',
        hash = `benson`,
    },
    benson2 = {
        name = 'Benson (Cluckin\' Bell)',
        brand = 'Vapid',
        model = 'benson2',
        price = 60635,
        category = 'commercial',
        type = 'automobile',
        hash = `benson2`,
    },
    besra = {
        name = 'Besra',
        brand = 'Western',
        model = 'besra',
        price = 2373206,
        category = 'planes',
        type = 'plane',
        hash = `besra`,
    },
    bestiagts = {
        name = 'Bestia GTS',
        brand = 'Grotti',
        model = 'bestiagts',
        price = 79703,
        category = 'sports',
        type = 'automobile',
        hash = `bestiagts`,
    },
    bf400 = {
        name = 'BF400',
        brand = 'Nagasaki',
        model = 'bf400',
        price = 23103,
        category = 'motorcycles',
        type = 'bike',
        hash = `bf400`,
    },
    bfinjection = {
        name = 'Injection',
        brand = 'BF',
        model = 'bfinjection',
        price = 67551,
        category = 'offroad',
        type = 'automobile',
        hash = `bfinjection`,
    },
    biff = {
        name = 'Biff',
        brand = 'HVY',
        model = 'biff',
        price = 56668,
        category = 'commercial',
        type = 'automobile',
        hash = `biff`,
    },
    bifta = {
        name = 'Bifta',
        brand = 'BF',
        model = 'bifta',
        price = 74872,
        category = 'offroad',
        type = 'automobile',
        hash = `bifta`,
    },
    bison = {
        name = 'Bison',
        brand = 'Bravado',
        model = 'bison',
        price = 62297,
        category = 'vans',
        type = 'automobile',
        hash = `bison`,
    },
    bison2 = {
        name = 'Bison (McGill-Olsen)',
        brand = 'Bravado',
        model = 'bison2',
        price = 62297,
        category = 'vans',
        type = 'automobile',
        hash = `bison2`,
    },
    bison3 = {
        name = 'Bison (The Mighty Bush)',
        brand = 'Bravado',
        model = 'bison3',
        price = 62297,
        category = 'vans',
        type = 'automobile',
        hash = `bison3`,
    },
    bjxl = {
        name = 'BeeJay XL',
        brand = 'Karin',
        model = 'bjxl',
        price = 60692,
        category = 'suvs',
        type = 'automobile',
        hash = `bjxl`,
    },
    blade = {
        name = 'Blade',
        brand = 'Vapid',
        model = 'blade',
        price = 70520,
        category = 'muscle',
        type = 'automobile',
        hash = `blade`,
    },
    blazer = {
        name = 'Blazer',
        brand = 'Nagasaki',
        model = 'blazer',
        price = 21457,
        category = 'offroad',
        type = 'automobile',
        hash = `blazer`,
    },
    blazer2 = {
        name = 'Blazer Lifeguard',
        brand = 'Nagasaki',
        model = 'blazer2',
        price = 15679,
        category = 'offroad',
        type = 'automobile',
        hash = `blazer2`,
    },
    blazer3 = {
        name = 'Hot Rod Blazer',
        brand = 'Nagasaki',
        model = 'blazer3',
        price = 21457,
        category = 'offroad',
        type = 'automobile',
        hash = `blazer3`,
    },
    blazer4 = {
        name = 'Blazer Sport',
        brand = 'Nagasaki',
        model = 'blazer4',
        price = 24453,
        category = 'offroad',
        type = 'automobile',
        hash = `blazer4`,
    },
    blazer5 = {
        name = 'Blazer Aqua',
        brand = 'Nagasaki',
        model = 'blazer5',
        price = 25680,
        category = 'offroad',
        type = 'automobile',
        hash = `blazer5`,
    },
    blimp = {
        name = 'Atomic Blimp',
        brand = '',
        model = 'blimp',
        price = 1024161,
        category = 'planes',
        type = 'plane',
        hash = `blimp`,
    },
    blimp2 = {
        name = 'Xero Blimp',
        brand = '',
        model = 'blimp2',
        price = 1036862,
        category = 'planes',
        type = 'plane',
        hash = `blimp2`,
    },
    blimp3 = {
        name = 'Blimp',
        brand = '',
        model = 'blimp3',
        price = 1024161,
        category = 'planes',
        type = 'plane',
        hash = `blimp3`,
    },
    blista = {
        name = 'Blista',
        brand = 'Dinka',
        model = 'blista',
        price = 69371,
        category = 'compacts',
        type = 'automobile',
        hash = `blista`,
    },
    blista2 = {
        name = 'Blista Compact',
        brand = 'Dinka',
        model = 'blista2',
        price = 69291,
        category = 'sports',
        type = 'automobile',
        hash = `blista2`,
    },
    blista3 = {
        name = 'Blista Go Go Monkey',
        brand = 'Dinka',
        model = 'blista3',
        price = 69291,
        category = 'sports',
        type = 'automobile',
        hash = `blista3`,
    },
    bmx = {
        name = 'BMX',
        brand = 'PED',
        model = 'bmx',
        price = 2735,
        category = 'cycles',
        type = 'bike',
        hash = `bmx`,
    },
    boattrailer = {
        name = 'Boat Trailer',
        brand = '',
        model = 'boattrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `boattrailer`,
    },
    boattrailer2 = {
        name = 'Boat Trailer (Dinghy)',
        brand = '',
        model = 'boattrailer2',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `boattrailer2`,
    },
    boattrailer3 = {
        name = 'Boat Trailer (Seashark)',
        brand = '',
        model = 'boattrailer3',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `boattrailer3`,
    },
    bobcatxl = {
        name = 'Bobcat XL Open',
        brand = 'Vapid',
        model = 'bobcatxl',
        price = 58720,
        category = 'vans',
        type = 'automobile',
        hash = `bobcatxl`,
    },
    bodhi2 = {
        name = 'Bodhi',
        brand = 'Canis',
        model = 'bodhi2',
        price = 59867,
        category = 'offroad',
        type = 'automobile',
        hash = `bodhi2`,
    },
    bombushka = {
        name = 'RM-10 Bombushka',
        brand = '',
        model = 'bombushka',
        price = 788207,
        category = 'planes',
        type = 'plane',
        hash = `bombushka`,
    },
    boor = {
        name = 'Boor',
        brand = 'Karin',
        model = 'boor',
        price = 67237,
        category = 'offroad',
        type = 'automobile',
        hash = `boor`,
    },
    boxville = {
        name = 'Boxville (LSDWP)',
        brand = 'Brute',
        model = 'boxville',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `boxville`,
    },
    boxville2 = {
        name = 'Boxville (Go Postal)',
        brand = 'Brute',
        model = 'boxville2',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `boxville2`,
    },
    boxville3 = {
        name = 'Boxville (Humane Labs)',
        brand = 'Brute',
        model = 'boxville3',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `boxville3`,
    },
    boxville4 = {
        name = 'Boxville (Post Op)',
        brand = 'Brute',
        model = 'boxville4',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `boxville4`,
    },
    boxville5 = {
        name = 'Armored Boxville',
        brand = 'Brute',
        model = 'boxville5',
        price = 65341,
        category = 'vans',
        type = 'automobile',
        hash = `boxville5`,
    },
    boxville6 = {
        name = 'Boxville (LSDS)',
        brand = 'Brute',
        model = 'boxville6',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `boxville6`,
    },
    brawler = {
        name = 'Brawler',
        brand = 'Coil',
        model = 'brawler',
        price = 77578,
        category = 'offroad',
        type = 'automobile',
        hash = `brawler`,
    },
    brickade = {
        name = 'Brickade',
        brand = 'MTL',
        model = 'brickade',
        price = 54416,
        category = 'service',
        type = 'automobile',
        hash = `brickade`,
    },
    brickade2 = {
        name = 'Brickade 6x6',
        brand = 'MTL',
        model = 'brickade2',
        price = 54416,
        category = 'service',
        type = 'automobile',
        hash = `brickade2`,
    },
    brigham = {
        name = 'Brigham',
        brand = 'Albany',
        model = 'brigham',
        price = 63825,
        category = 'muscle',
        type = 'automobile',
        hash = `brigham`,
    },
    brioso = {
        name = 'Brioso R/A',
        brand = 'Grotti',
        model = 'brioso',
        price = 69113,
        category = 'compacts',
        type = 'automobile',
        hash = `brioso`,
    },
    brioso2 = {
        name = 'Brioso 300',
        brand = 'Grotti',
        model = 'brioso2',
        price = 54291,
        category = 'compacts',
        type = 'automobile',
        hash = `brioso2`,
    },
    brioso3 = {
        name = 'Brioso 300 Widebody',
        brand = 'Grotti',
        model = 'brioso3',
        price = 61333,
        category = 'compacts',
        type = 'automobile',
        hash = `brioso3`,
    },
    broadway = {
        name = 'Broadway',
        brand = 'Classique',
        model = 'broadway',
        price = 61702,
        category = 'muscle',
        type = 'automobile',
        hash = `broadway`,
    },
    bruiser = {
        name = 'Apocalypse Bruiser',
        brand = 'Benefactor',
        model = 'bruiser',
        price = 63687,
        category = 'offroad',
        type = 'automobile',
        hash = `bruiser`,
    },
    bruiser2 = {
        name = 'Future Shock Bruiser',
        brand = 'Benefactor',
        model = 'bruiser2',
        price = 63687,
        category = 'offroad',
        type = 'automobile',
        hash = `bruiser2`,
    },
    bruiser3 = {
        name = 'Nightmare Bruiser',
        brand = 'Benefactor',
        model = 'bruiser3',
        price = 63687,
        category = 'offroad',
        type = 'automobile',
        hash = `bruiser3`,
    },
    brutus = {
        name = 'Apocalypse Brutus',
        brand = 'Declasse',
        model = 'brutus',
        price = 70122,
        category = 'offroad',
        type = 'automobile',
        hash = `brutus`,
    },
    brutus2 = {
        name = 'Future Shock Brutus',
        brand = 'Declasse',
        model = 'brutus2',
        price = 70122,
        category = 'offroad',
        type = 'automobile',
        hash = `brutus2`,
    },
    brutus3 = {
        name = 'Nightmare Brutus',
        brand = 'Declasse',
        model = 'brutus3',
        price = 70122,
        category = 'offroad',
        type = 'automobile',
        hash = `brutus3`,
    },
    btype = {
        name = 'Roosevelt',
        brand = 'Albany',
        model = 'btype',
        price = 69018,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `btype`,
    },
    btype2 = {
        name = 'Franken Stange',
        brand = 'Albany',
        model = 'btype2',
        price = 74624,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `btype2`,
    },
    btype3 = {
        name = 'Roosevelt Valor',
        brand = 'Albany',
        model = 'btype3',
        price = 69018,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `btype3`,
    },
    buccaneer = {
        name = 'Buccaneer',
        brand = 'Albany',
        model = 'buccaneer',
        price = 76634,
        category = 'muscle',
        type = 'automobile',
        hash = `buccaneer`,
    },
    buccaneer2 = {
        name = 'Buccaneer Custom',
        brand = 'Albany',
        model = 'buccaneer2',
        price = 76634,
        category = 'muscle',
        type = 'automobile',
        hash = `buccaneer2`,
    },
    buffalo = {
        name = 'Buffalo',
        brand = 'Bravado',
        model = 'buffalo',
        price = 75156,
        category = 'sports',
        type = 'automobile',
        hash = `buffalo`,
    },
    buffalo2 = {
        name = 'Buffalo S',
        brand = 'Bravado',
        model = 'buffalo2',
        price = 77026,
        category = 'sports',
        type = 'automobile',
        hash = `buffalo2`,
    },
    buffalo3 = {
        name = 'Sprunk Buffalo',
        brand = 'Bravado',
        model = 'buffalo3',
        price = 80264,
        category = 'sports',
        type = 'automobile',
        hash = `buffalo3`,
    },
    buffalo4 = {
        name = 'Buffalo STX',
        brand = 'Bravado',
        model = 'buffalo4',
        price = 80516,
        category = 'muscle',
        type = 'automobile',
        hash = `buffalo4`,
    },
    buffalo5 = {
        name = 'Buffalo EVX',
        brand = 'Bravado',
        model = 'buffalo5',
        price = 83332,
        category = 'muscle',
        type = 'automobile',
        hash = `buffalo5`,
    },
    bulldozer = {
        name = 'Dozer',
        brand = 'HVY',
        model = 'bulldozer',
        price = 9376,
        category = 'industrial',
        type = 'automobile',
        hash = `bulldozer`,
    },
    bullet = {
        name = 'Bullet',
        brand = 'Vapid',
        model = 'bullet',
        price = 82565,
        category = 'super',
        type = 'automobile',
        hash = `bullet`,
    },
    burrito = {
        name = 'Burrito (LSDWP)',
        brand = 'Declasse',
        model = 'burrito',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `burrito`,
    },
    burrito2 = {
        name = 'Bugstars Burrito',
        brand = 'Declasse',
        model = 'burrito2',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `burrito2`,
    },
    burrito3 = {
        name = 'Burrito',
        brand = 'Declasse',
        model = 'burrito3',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `burrito3`,
    },
    burrito4 = {
        name = 'Burrito (McGill-Olsen)',
        brand = 'Declasse',
        model = 'burrito4',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `burrito4`,
    },
    burrito5 = {
        name = 'Burrito (Snow)',
        brand = 'Declasse',
        model = 'burrito5',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `burrito5`,
    },
    bus = {
        name = 'Bus',
        brand = '',
        model = 'bus',
        price = 42481,
        category = 'service',
        type = 'automobile',
        hash = `bus`,
    },
    buzzard = {
        name = 'Buzzard Attack Chopper',
        brand = 'Nagasaki',
        model = 'buzzard',
        price = 6462758,
        category = 'helicopters',
        type = 'heli',
        hash = `buzzard`,
    },
    buzzard2 = {
        name = 'Buzzard',
        brand = 'Nagasaki',
        model = 'buzzard2',
        price = 6462758,
        category = 'helicopters',
        type = 'heli',
        hash = `buzzard2`,
    },
    cablecar = {
        name = 'Cable Car',
        brand = '',
        model = 'cablecar',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `cablecar`,
    },
    caddy = {
        name = 'Caddy (Golf)',
        brand = 'Nagasaki',
        model = 'caddy',
        price = 36554,
        category = 'utility',
        type = 'automobile',
        hash = `caddy`,
    },
    caddy2 = {
        name = 'Caddy',
        brand = 'Nagasaki',
        model = 'caddy2',
        price = 36554,
        category = 'utility',
        type = 'automobile',
        hash = `caddy2`,
    },
    caddy3 = {
        name = 'Caddy (Bunker)',
        brand = 'Nagasaki',
        model = 'caddy3',
        price = 36022,
        category = 'utility',
        type = 'automobile',
        hash = `caddy3`,
    },
    calico = {
        name = 'Calico GTF',
        brand = 'Karin',
        model = 'calico',
        price = 82150,
        category = 'sports',
        type = 'automobile',
        hash = `calico`,
    },
    camper = {
        name = 'Camper',
        brand = 'Brute',
        model = 'camper',
        price = 50131,
        category = 'vans',
        type = 'automobile',
        hash = `camper`,
    },
    caracara = {
        name = 'Caracara',
        brand = 'Vapid',
        model = 'caracara',
        price = 63685,
        category = 'offroad',
        type = 'automobile',
        hash = `caracara`,
    },
    caracara2 = {
        name = 'Caracara 4x4',
        brand = 'Vapid',
        model = 'caracara2',
        price = 64784,
        category = 'offroad',
        type = 'automobile',
        hash = `caracara2`,
    },
    carbonizzare = {
        name = 'Carbonizzare',
        brand = 'Grotti',
        model = 'carbonizzare',
        price = 80346,
        category = 'sports',
        type = 'automobile',
        hash = `carbonizzare`,
    },
    carbonrs = {
        brand = 'Nagasaki',
        name = 'Carbon RS',
        model = 'carbonrs',
        price = 24649,
        category = 'motorcycles',
        type = 'bike',
        hash = `carbonrs`,
    },
    cargobob = {
        name = 'Cargobob',
        brand = 'Western',
        model = 'cargobob',
        price = 5616986,
        category = 'helicopters',
        type = 'heli',
        hash = `cargobob`,
    },
    cargobob2 = {
        name = 'Cargobob (Jetsam)',
        brand = 'Western',
        model = 'cargobob2',
        price = 5616986,
        category = 'helicopters',
        type = 'heli',
        hash = `cargobob2`,
    },
    cargobob3 = {
        name = 'Cargobob (Trevor Philips Enterprises)',
        brand = 'Western',
        model = 'cargobob3',
        price = 5616986,
        category = 'helicopters',
        type = 'heli',
        hash = `cargobob3`,
    },
    cargobob4 = {
        name = 'Cargobob (Drop Zone)',
        brand = 'Western',
        model = 'cargobob4',
        price = 5616986,
        category = 'helicopters',
        type = 'heli',
        hash = `cargobob4`,
    },
    cargobob5 = {
        name = 'DH-7 Iron Mule',
        brand = 'Buckingham',
        model = 'cargobob5',
        price = 5616986,
        category = 'helicopters',
        type = 'heli',
        hash = `cargobob5`,
    },
    cargoplane = {
        name = 'Cargo Plane',
        brand = '',
        model = 'cargoplane',
        price = 1537948,
        category = 'planes',
        type = 'plane',
        hash = `cargoplane`,
    },
    cargoplane2 = {
        name = 'Cargo Plane',
        brand = '',
        model = 'cargoplane2',
        price = 1537948,
        category = 'planes',
        type = 'plane',
        hash = `cargoplane2`,
    },
    casco = {
        name = 'Casco',
        brand = 'Lampadati',
        model = 'casco',
        price = 83125,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `casco`,
    },
    castigator = {
        name = 'Castigator',
        brand = 'Canis',
        model = 'castigator',
        price = 83125,
        category = 'suvs',
        type = 'automobile',
        hash = `castigator`,
    },
    cavalcade = {
        name = 'Cavalcade',
        brand = 'Albany',
        model = 'cavalcade',
        price = 63694,
        category = 'suvs',
        type = 'automobile',
        hash = `cavalcade`,
    },
    cavalcade2 = {
        name = 'Cavalcade',
        brand = 'Albany',
        model = 'cavalcade2',
        price = 63694,
        category = 'suvs',
        type = 'automobile',
        hash = `cavalcade2`,
    },
    cavalcade3 = {
        name = 'Cavalcade XL',
        brand = 'Albany',
        model = 'cavalcade3',
        price = 67959,
        category = 'suvs',
        type = 'automobile',
        hash = `cavalcade3`,
    },
    cerberus = {
        name = 'Apocalypse Cerberus',
        brand = 'MTL',
        model = 'cerberus',
        price = 61154,
        category = 'commercial',
        type = 'automobile',
        hash = `cerberus`,
    },
    cerberus2 = {
        name = 'Future Shock Cerberus',
        brand = 'MTL',
        model = 'cerberus2',
        price = 61154,
        category = 'commercial',
        type = 'automobile',
        hash = `cerberus2`,
    },
    cerberus3 = {
        name = 'Nightmare Cerberus',
        brand = 'MTL',
        model = 'cerberus3',
        price = 61154,
        category = 'commercial',
        type = 'automobile',
        hash = `cerberus3`,
    },
    champion = {
        name = 'Champion',
        brand = 'Dewbauchee',
        model = 'champion',
        price = 86582,
        category = 'super',
        type = 'automobile',
        hash = `champion`,
    },
    chavosv6 = {
        name = 'Chavos V6',
        brand = 'Dewbauchee',
        model = 'chavosv6',
        price = 86582,
        category = 'sedans',
        type = 'automobile',
        hash = `chavosv6`,
    },
    cheburek = {
        name = 'Cheburek',
        brand = 'RUNE',
        model = 'cheburek',
        price = 74166,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `cheburek`,
    },
    cheetah = {
        name = 'Cheetah',
        brand = 'Grotti',
        model = 'cheetah',
        price = 82927,
        category = 'super',
        type = 'automobile',
        hash = `cheetah`,
    },
    cheetah2 = {
        name = 'Cheetah Classic',
        brand = 'Grotti',
        model = 'cheetah2',
        price = 82724,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `cheetah2`,
    },
    chernobog = {
        name = 'Chernobog',
        brand = 'HVY',
        model = 'chernobog',
        price = 37291,
        category = 'military',
        type = 'automobile',
        hash = `chernobog`,
    },
    chimera = {
        name = 'Chimera',
        brand = 'Nagasaki',
        model = 'chimera',
        price = 22412,
        category = 'motorcycles',
        type = 'bike',
        hash = `chimera`,
    },
    chino = {
        name = 'Chino',
        brand = 'Vapid',
        model = 'chino',
        price = 58651,
        category = 'muscle',
        type = 'automobile',
        hash = `chino`,
    },
    chino2 = {
        name = 'Chino Custom',
        brand = 'Vapid',
        model = 'chino2',
        price = 60394,
        category = 'muscle',
        type = 'automobile',
        hash = `chino2`,
    },
    cinquemila = {
        name = 'Cinquemila',
        brand = 'Lampadati',
        model = 'cinquemila',
        price = 80476,
        category = 'sedans',
        type = 'automobile',
        hash = `cinquemila`,
    },
    cliffhanger = {
        name = 'Cliffhanger',
        brand = 'Western',
        model = 'cliffhanger',
        price = 25621,
        category = 'motorcycles',
        type = 'bike',
        hash = `cliffhanger`,
    },
    clique = {
        name = 'Clique',
        brand = 'Vapid',
        model = 'clique',
        price = 76525,
        category = 'muscle',
        type = 'automobile',
        hash = `clique`,
    },
    clique2 = {
        name = 'Clique Wagon',
        brand = 'Vapid',
        model = 'clique2',
        price = 54902,
        category = 'muscle',
        type = 'automobile',
        hash = `clique2`,
    },
    club = {
        name = 'Club',
        brand = 'BF',
        model = 'club',
        price = 69335,
        category = 'compacts',
        type = 'automobile',
        hash = `club`,
    },
    coach = {
        name = 'Dashound',
        brand = '',
        model = 'coach',
        price = 42321,
        category = 'service',
        type = 'automobile',
        hash = `coach`,
    },
    cog55 = {
        name = 'Cognoscenti 55',
        brand = 'Enus',
        model = 'cog55',
        price = 76441,
        category = 'sedans',
        type = 'automobile',
        hash = `cog55`,
    },
    cog552 = {
        name = 'Cognoscenti 55 (Armored)',
        brand = 'Enus',
        model = 'cog552',
        price = 75535,
        category = 'sedans',
        type = 'automobile',
        hash = `cog552`,
    },
    cogcabrio = {
        name = 'Cognoscenti Cabrio',
        brand = 'Enus',
        model = 'cogcabrio',
        price = 74712,
        category = 'coupes',
        type = 'automobile',
        hash = `cogcabrio`,
    },
    cognoscenti = {
        name = 'Cognoscenti',
        brand = 'Enus',
        model = 'cognoscenti',
        price = 75535,
        category = 'sedans',
        type = 'automobile',
        hash = `cognoscenti`,
    },
    cognoscenti2 = {
        name = 'Cognoscenti (Armored)',
        brand = 'Enus',
        model = 'cognoscenti2',
        price = 74605,
        category = 'sedans',
        type = 'automobile',
        hash = `cognoscenti2`,
    },
    comet2 = {
        name = 'Comet',
        brand = 'Pfister',
        model = 'comet2',
        price = 83289,
        category = 'sports',
        type = 'automobile',
        hash = `comet2`,
    },
    comet3 = {
        name = 'Comet Retro Custom',
        brand = 'Pfister',
        model = 'comet3',
        price = 83253,
        category = 'sports',
        type = 'automobile',
        hash = `comet3`,
    },
    comet4 = {
        name = 'Comet Safari',
        brand = 'Pfister',
        model = 'comet4',
        price = 78289,
        category = 'sports',
        type = 'automobile',
        hash = `comet4`,
    },
    comet5 = {
        name = 'Comet SR',
        brand = 'Pfister',
        model = 'comet5',
        price = 78583,
        category = 'sports',
        type = 'automobile',
        hash = `comet5`,
    },
    comet6 = {
        name = 'Comet S2',
        brand = 'Pfister',
        model = 'comet6',
        price = 83378,
        category = 'sports',
        type = 'automobile',
        hash = `comet6`,
    },
    comet7 = {
        name = 'Comet S2 Cabrio',
        brand = 'Pfister',
        model = 'comet7',
        price = 83759,
        category = 'sports',
        type = 'automobile',
        hash = `comet7`,
    },
    conada = {
        name = 'Conada',
        brand = 'Buckingham',
        model = 'conada',
        price = 6931736,
        category = 'helicopters',
        type = 'heli',
        hash = `conada`,
    },
    conada2 = {
        name = 'Weaponized Conada',
        brand = 'Buckingham',
        model = 'conada2',
        price = 6855740,
        category = 'helicopters',
        type = 'heli',
        hash = `conada2`,
    },
    contender = {
        name = 'Contender',
        brand = 'Vapid',
        model = 'contender',
        price = 68578,
        category = 'suvs',
        type = 'automobile',
        hash = `contender`,
    },
    coquette = {
        name = 'Coquette',
        brand = 'Invetero',
        model = 'coquette',
        price = 83674,
        category = 'sports',
        type = 'automobile',
        hash = `coquette`,
    },
    coquette2 = {
        name = 'Coquette Classic',
        brand = 'Invetero',
        model = 'coquette2',
        price = 81755,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `coquette2`,
    },
    coquette3 = {
        name = 'Coquette BlackFin',
        brand = 'Invetero',
        model = 'coquette3',
        price = 74621,
        category = 'muscle',
        type = 'automobile',
        hash = `coquette3`,
    },
    coquette4 = {
        name = 'Coquette D10',
        brand = 'Invetero',
        model = 'coquette4',
        price = 80771,
        category = 'sports',
        type = 'automobile',
        hash = `coquette4`,
    },
    coquette5 = {
        name = 'Coquette D1',
        brand = 'Invetero',
        model = 'coquette5',
        price = 80771,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `coquette5`,
    },
    coquette6 = {
        name = 'Coquette D5',
        brand = 'Invetero',
        model = 'coquette6',
        price = 80771,
        category = 'sports',
        type = 'automobile',
        hash = `coquette6`,
    },
    corsita = {
        name = 'Corsita',
        brand = 'Lampadati',
        model = 'corsita',
        price = 87616,
        category = 'sports',
        type = 'automobile',
        hash = `corsita`,
    },
    coureur = {
        name = 'La Coureuse',
        brand = 'Penaud',
        model = 'coureur',
        price = 77754,
        category = 'sports',
        type = 'automobile',
        hash = `coureur`,
    },
    cruiser = {
        name = 'Cruiser',
        brand = 'PED',
        model = 'cruiser',
        price = 2751,
        category = 'cycles',
        type = 'bike',
        hash = `cruiser`,
    },
    crusader = {
        name = 'Crusader',
        brand = 'Canis',
        model = 'crusader',
        price = 59206,
        category = 'military',
        type = 'automobile',
        hash = `crusader`,
    },
    cuban800 = {
        name = 'Cuban 800',
        brand = 'Western',
        model = 'cuban800',
        price = 1489140,
        category = 'planes',
        type = 'plane',
        hash = `cuban800`,
    },
    cutter = {
        name = 'Cutter',
        brand = 'HVY',
        model = 'cutter',
        price = 22017,
        category = 'industrial',
        type = 'automobile',
        hash = `cutter`,
    },
    cyclone = {
        name = 'Cyclone',
        brand = 'Coil',
        model = 'cyclone',
        price = 68875,
        category = 'super',
        type = 'automobile',
        hash = `cyclone`,
    },
    cypher = {
        name = 'Cypher',
        brand = 'Übermacht',
        model = 'cypher',
        price = 75676,
        category = 'sports',
        type = 'automobile',
        hash = `cypher`,
    },
    daemon = {
        name = 'Daemon',
        brand = 'Western',
        model = 'daemon',
        price = 22321,
        category = 'motorcycles',
        type = 'bike',
        hash = `daemon`,
    },
    daemon2 = {
        name = 'Daemon Custom',
        brand = 'Western',
        model = 'daemon2',
        price = 22423,
        category = 'motorcycles',
        type = 'bike',
        hash = `daemon2`,
    },
    deathbike = {
        name = 'Apocalypse Deathbike',
        brand = 'Western',
        model = 'deathbike',
        price = 25195,
        category = 'motorcycles',
        type = 'bike',
        hash = `deathbike`,
    },
    deathbike2 = {
        name = 'Future Shock Deathbike',
        brand = 'Western',
        model = 'deathbike2',
        price = 25195,
        category = 'motorcycles',
        type = 'bike',
        hash = `deathbike2`,
    },
    deathbike3 = {
        name = 'Nightmare Deathbike',
        brand = 'Western',
        model = 'deathbike3',
        price = 25195,
        category = 'motorcycles',
        type = 'bike',
        hash = `deathbike3`,
    },
    defiler = {
        name = 'Defiler',
        brand = 'Shitzu',
        model = 'defiler',
        price = 25616,
        category = 'motorcycles',
        type = 'bike',
        hash = `defiler`,
    },
    deity = {
        name = 'Deity',
        brand = 'Enus',
        model = 'deity',
        price = 73581,
        category = 'sedans',
        type = 'automobile',
        hash = `deity`,
    },
    deluxo = {
        name = 'Deluxo',
        brand = 'Imponte',
        model = 'deluxo',
        price = 70738,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `deluxo`,
    },
    deveste = {
        name = 'Deveste',
        brand = 'Principe',
        model = 'deveste',
        price = 87916,
        category = 'super',
        type = 'automobile',
        hash = `deveste`,
    },
    deviant = {
        name = 'Deviant',
        brand = 'Schyster',
        model = 'deviant',
        price = 70267,
        category = 'muscle',
        type = 'automobile',
        hash = `deviant`,
    },
    diablous = {
        name = 'Diablous',
        brand = 'Principe',
        model = 'diablous',
        price = 24745,
        category = 'motorcycles',
        type = 'bike',
        hash = `diablous`,
    },
    diablous2 = {
        name = 'Diablous Custom',
        brand = 'Principe',
        model = 'diablous2',
        price = 24901,
        category = 'motorcycles',
        type = 'bike',
        hash = `diablous2`,
    },
    dilettante = {
        name = 'Dilettante',
        brand = 'Karin',
        model = 'dilettante',
        price = 40298,
        category = 'compacts',
        type = 'automobile',
        hash = `dilettante`,
    },
    dilettante2 = {
        name = 'Dilettante (Security)',
        brand = 'Karin',
        model = 'dilettante2',
        price = 40298,
        category = 'compacts',
        type = 'automobile',
        hash = `dilettante2`,
    },
    dinghy = {
        name = 'Dinghy',
        brand = 'Nagasaki',
        model = 'dinghy',
        price = 446680,
        category = 'boats',
        type = 'boat',
        hash = `dinghy`,
    },
    dinghy2 = {
        name = 'Dinghy',
        brand = 'Nagasaki',
        model = 'dinghy2',
        price = 446680,
        category = 'boats',
        type = 'boat',
        hash = `dinghy2`,
    },
    dinghy3 = {
        name = 'Dinghy (Heist)',
        brand = 'Nagasaki',
        model = 'dinghy3',
        price = 446680,
        category = 'boats',
        type = 'boat',
        hash = `dinghy3`,
    },
    dinghy4 = {
        name = 'Dinghy (Yacht)',
        brand = 'Nagasaki',
        model = 'dinghy4',
        price = 446680,
        category = 'boats',
        type = 'boat',
        hash = `dinghy4`,
    },
    dinghy5 = {
        name = 'Weaponized Dinghy',
        brand = 'Nagasaki',
        model = 'dinghy5',
        price = 446680,
        category = 'boats',
        type = 'boat',
        hash = `dinghy5`,
    },
    dloader = {
        name = 'Duneloader',
        brand = 'Bravado',
        model = 'dloader',
        price = 55231,
        category = 'offroad',
        type = 'automobile',
        hash = `dloader`,
    },
    docktrailer = {
        name = 'Dock Trailer',
        brand = '',
        model = 'docktrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `docktrailer`,
    },
    docktug = {
        name = 'Docktug',
        brand = 'HVY',
        model = 'docktug',
        price = 41473,
        category = 'utility',
        type = 'automobile',
        hash = `docktug`,
    },
    dodo = {
        name = 'Dodo',
        brand = 'Mammoth',
        model = 'dodo',
        price = 1321065,
        category = 'planes',
        type = 'plane',
        hash = `dodo`,
    },
    dominator = {
        name = 'Dominator',
        brand = 'Vapid',
        model = 'dominator',
        price = 80149,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator`,
    },
    dominator2 = {
        name = 'Pisswasser Dominator',
        brand = 'Vapid',
        model = 'dominator2',
        price = 81440,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator2`,
    },
    dominator3 = {
        name = 'Dominator GTX',
        brand = 'Vapid',
        model = 'dominator3',
        price = 77862,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator3`,
    },
    dominator4 = {
        name = 'Dominator Arena',
        brand = 'Vapid',
        model = 'dominator4',
        price = 80565,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator4`,
    },
    dominator5 = {
        name = 'Future Shock Dominator',
        brand = 'Vapid',
        model = 'dominator5',
        price = 80565,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator5`,
    },
    dominator6 = {
        name = 'Nightmare Dominator',
        brand = 'Vapid',
        model = 'dominator6',
        price = 80565,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator6`,
    },
    dominator7 = {
        name = 'Dominator ASP',
        brand = 'Vapid',
        model = 'dominator7',
        price = 83320,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator7`,
    },
    dominator8 = {
        name = 'Dominator GTT',
        brand = 'Vapid',
        model = 'dominator8',
        price = 76296,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator8`,
    },
    dominator9 = {
        name = 'Dominator GT',
        brand = 'Vapid',
        model = 'dominator9',
        price = 82702,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator9`,
    },
    dominator10 = {
        name = 'Dominator FX',
        brand = 'Vapid',
        model = 'dominator10',
        price = 82702,
        category = 'muscle',
        type = 'automobile',
        hash = `dominator10`,
    },
    dorado = {
        name = 'Dorado',
        brand = 'Bravado',
        model = 'dorado',
        price = 69578,
        category = 'suvs',
        type = 'automobile',
        hash = `dorado`,
    },
    double = {
        name = 'Double-T',
        brand = 'Dinka',
        model = 'double',
        price = 25173,
        category = 'motorcycles',
        type = 'bike',
        hash = `double`,
    },
    drafter = {
        name = '8F Drafter',
        brand = 'Obey',
        model = 'drafter',
        price = 79907,
        category = 'sports',
        type = 'automobile',
        hash = `drafter`,
    },
    draugur = {
        name = 'Draugur',
        brand = 'Declasse',
        model = 'draugur',
        price = 72622,
        category = 'offroad',
        type = 'automobile',
        hash = `draugur`,
    },
    driftcheburek = {
        name = 'Cheburek (Drift)',
        brand = 'RUNE',
        model = 'driftcheburek',
        price = 76163,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `driftcheburek`,
    },
    driftcypher = {
        name = 'Cypher (Drift)',
        brand = 'Übermacht',
        model = 'driftcypher',
        price = 76163,
        category = 'sports',
        type = 'automobile',
        hash = `driftcypher`,
    },
    drifteuros = {
        name = 'Euros (Drift)',
        brand = 'Annis',
        model = 'drifteuros',
        price = 76163,
        category = 'sports',
        type = 'automobile',
        hash = `drifteuros`,
    },
    driftfr36 = {
        name = 'FR36 (Drift)',
        brand = 'Fathom',
        model = 'driftfr36',
        price = 75045,
        category = 'coupes',
        type = 'automobile',
        hash = `driftfr36`,
    },
    driftfuto = {
        name = 'Futo GTX (Drift)',
        brand = 'Karin',
        model = 'driftfuto',
        price = 75037,
        category = 'sports',
        type = 'automobile',
        hash = `driftfuto`,
    },
    driftfuto2 = {
        name = 'Futo (Drift)',
        brand = 'Karin',
        model = 'driftfuto2',
        price = 75037,
        category = 'sports',
        type = 'automobile',
        hash = `driftfuto2`,
    },
    driftjester = {
        name = 'Jester RR (Drift)',
        brand = 'Dinka',
        model = 'driftjester',
        price = 74933,
        category = 'sports',
        type = 'automobile',
        hash = `driftjester`,
    },
    driftjester3 = {
        name = 'Jester Classic (Drift)',
        brand = 'Dinka',
        model = 'driftjester3',
        price = 74933,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `driftjester3`,
    },
    driftnebula = {
        name = 'Nebula Turbo (Drift)',
        brand = 'Vulcar',
        model = 'driftnebula',
        price = 74933,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `driftnebula`,
    },
    driftremus = {
        name = 'Remus (Drift)',
        brand = 'Annis',
        model = 'driftremus',
        price = 78305,
        category = 'sports',
        type = 'automobile',
        hash = `driftremus`,
    },
    driftsentinel = {
        name = 'Sentinel Classic Widebody (Drift)',
        brand = 'Übermacht',
        model = 'driftsentinel',
        price = 78305,
        category = 'sports',
        type = 'automobile',
        hash = `driftsentinel`,
    },
    drifttampa = {
        name = 'Drift Tampa',
        brand = 'Declasse',
        model = 'drifttampa',
        price = 78299,
        category = 'sports',
        type = 'automobile',
        hash = `drifttampa`,
    },
    driftvorschlag = {
        name = 'Vorschlaghammer (Drift)',
        brand = 'Declasse',
        model = 'driftvorschlag',
        price = 70884,
        category = 'sedans',
        type = 'automobile',
        hash = `driftvorschlag`,
    },
    driftyosemite = {
        name = 'Drift Yosemite',
        brand = 'Declasse',
        model = 'driftyosemite',
        price = 70884,
        category = 'muscle',
        type = 'automobile',
        hash = `driftyosemite`,
    },
    driftzr350 = {
        name = 'ZR350 (Drift)',
        brand = 'Annis',
        model = 'driftzr350',
        price = 75060,
        category = 'sports',
        type = 'automobile',
        hash = `driftzr350`,
    },
    dubsta = {
        name = 'Dubsta',
        brand = 'Benefactor',
        model = 'dubsta',
        price = 62617,
        category = 'suvs',
        type = 'automobile',
        hash = `dubsta`,
    },
    dubsta2 = {
        name = 'Dubsta',
        brand = 'Benefactor',
        model = 'dubsta2',
        price = 62617,
        category = 'suvs',
        type = 'automobile',
        hash = `dubsta2`,
    },
    dubsta3 = {
        name = 'Dubsta 6x6',
        brand = 'Benefactor',
        model = 'dubsta3',
        price = 64556,
        category = 'offroad',
        type = 'automobile',
        hash = `dubsta3`,
    },
    dukes = {
        name = 'Dukes',
        brand = 'Imponte',
        model = 'dukes',
        price = 79383,
        category = 'muscle',
        type = 'automobile',
        hash = `dukes`,
    },
    dukes2 = {
        name = 'Dukes Nightrider',
        brand = 'Imponte',
        model = 'dukes2',
        price = 77611,
        category = 'muscle',
        type = 'automobile',
        hash = `dukes2`,
    },
    dukes3 = {
        name = 'Beater Dukes',
        brand = 'Imponte',
        model = 'dukes3',
        price = 78577,
        category = 'muscle',
        type = 'automobile',
        hash = `dukes3`,
    },
    dump = {
        name = 'Dump',
        brand = 'HVY',
        model = 'dump',
        price = 24629,
        category = 'industrial',
        type = 'automobile',
        hash = `dump`,
    },
    dune = {
        name = 'Dune Buggy',
        brand = 'BF',
        model = 'dune',
        price = 64617,
        category = 'offroad',
        type = 'automobile',
        hash = `dune`,
    },
    dune2 = {
        name = 'Space Docker',
        brand = 'BF',
        model = 'dune2',
        price = 63085,
        category = 'offroad',
        type = 'automobile',
        hash = `dune2`,
    },
    dune3 = {
        name = 'Dune FAV',
        brand = 'BF',
        model = 'dune3',
        price = 64617,
        category = 'offroad',
        type = 'automobile',
        hash = `dune3`,
    },
    dune4 = {
        name = 'Ramp Buggy',
        brand = 'BF',
        model = 'dune4',
        price = 82003,
        category = 'offroad',
        type = 'automobile',
        hash = `dune4`,
    },
    dune5 = {
        name = 'Ramp Buggy',
        brand = 'BF',
        model = 'dune5',
        price = 81376,
        category = 'offroad',
        type = 'automobile',
        hash = `dune5`,
    },
    duster = {
        name = 'Duster',
        brand = 'Western',
        model = 'duster',
        price = 1321065,
        category = 'planes',
        type = 'plane',
        hash = `duster`,
    },
    duster2 = {
        name = 'Duster 300-H',
        brand = 'Western',
        model = 'duster2',
        price = 1321065,
        category = 'planes',
        type = 'plane',
        hash = `duster2`,
    },
    dynasty = {
        name = 'Dynasty',
        brand = 'Weeny',
        model = 'dynasty',
        price = 58840,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `dynasty`,
    },
    elegy = {
        name = 'Elegy Retro Custom',
        brand = 'Annis',
        model = 'elegy',
        price = 78735,
        category = 'sports',
        type = 'automobile',
        hash = `elegy`,
    },
    elegy2 = {
        name = 'Elegy RH8',
        brand = 'Annis',
        model = 'elegy2',
        price = 80339,
        category = 'sports',
        type = 'automobile',
        hash = `elegy2`,
    },
    ellie = {
        name = 'Ellie',
        brand = 'Vapid',
        model = 'ellie',
        price = 75782,
        category = 'muscle',
        type = 'automobile',
        hash = `ellie`,
    },
    emerus = {
        name = 'Progen Emerus',
        brand = 'Progen',
        model = 'emerus',
        price = 85112,
        category = 'super',
        type = 'automobile',
        hash = `emerus`,
    },
    emperor = {
        name = 'Emperor',
        brand = 'Albany',
        model = 'emperor',
        price = 56110,
        category = 'sedans',
        type = 'automobile',
        hash = `emperor`,
    },
    emperor2 = {
        name = 'Emperor (Beater)',
        brand = 'Albany',
        model = 'emperor2',
        price = 56110,
        category = 'sedans',
        type = 'automobile',
        hash = `emperor2`,
    },
    emperor3 = {
        name = 'Emperor (Snow)',
        brand = 'Albany',
        model = 'emperor3',
        price = 56110,
        category = 'sedans',
        type = 'automobile',
        hash = `emperor3`,
    },
    enduro = {
        name = 'Enduro',
        brand = 'Dinka',
        model = 'enduro',
        price = 20848,
        category = 'motorcycles',
        type = 'bike',
        hash = `enduro`,
    },
    entity2 = {
        name = 'Entity XXR',
        brand = 'Överflöd',
        model = 'entity2',
        price = 89286,
        category = 'super',
        type = 'automobile',
        hash = `entity2`,
    },
    entity3 = {
        name = 'Entity MT',
        brand = 'Överflöd',
        model = 'entity3',
        price = 89208,
        category = 'super',
        type = 'automobile',
        hash = `entity3`,
    },
    entityxf = {
        name = 'Entity XF',
        brand = 'Överflöd',
        model = 'entityxf',
        price = 84601,
        category = 'super',
        type = 'automobile',
        hash = `entityxf`,
    },
    esskey = {
        name = 'Esskey',
        brand = 'Pegassi',
        model = 'esskey',
        price = 23138,
        category = 'motorcycles',
        type = 'bike',
        hash = `esskey`,
    },
    eudora = {
        name = 'Eudora',
        brand = 'Willard',
        model = 'eudora',
        price = 66819,
        category = 'muscle',
        type = 'automobile',
        hash = `eudora`,
    },
    euros = {
        name = 'Euros',
        brand = 'Annis',
        model = 'euros',
        price = 78840,
        category = 'sports',
        type = 'automobile',
        hash = `euros`,
    },
    eurosx32 = {
        name = 'Euros X32',
        brand = 'Annis',
        model = 'eurosx32',
        price = 78840,
        category = 'sports',
        type = 'automobile',
        hash = `eurosx32`,
    },
    everon = {
        name = 'Everon',
        brand = 'Karin',
        model = 'everon',
        price = 68803,
        category = 'offroad',
        type = 'automobile',
        hash = `everon`,
    },
    everon2 = {
        name = 'Hotring Everon',
        brand = 'Karin',
        model = 'everon2',
        price = 79842,
        category = 'sports',
        type = 'automobile',
        hash = `everon2`,
    },
    envisage = {
        name = 'Envisage',
        brand = 'Bollokan',
        model = 'envisage',
        price = 79842,
        category = 'sports',
        type = 'automobile',
        hash = `envisage`,
    },
    exemplar = {
        name = 'Exemplar',
        brand = 'Dewbauchee',
        model = 'exemplar',
        price = 79891,
        category = 'coupes',
        type = 'automobile',
        hash = `exemplar`,
    },
    f620 = {
        name = 'F620',
        brand = 'Ocelot',
        model = 'f620',
        price = 79607,
        category = 'coupes',
        type = 'automobile',
        hash = `f620`,
    },
    faction = {
        name = 'Faction',
        brand = 'Willard',
        model = 'faction',
        price = 77450,
        category = 'muscle',
        type = 'automobile',
        hash = `faction`,
    },
    faction2 = {
        name = 'Faction Rider',
        brand = 'Willard',
        model = 'faction2',
        price = 77450,
        category = 'muscle',
        type = 'automobile',
        hash = `faction2`,
    },
    faction3 = {
        name = 'Faction Custom Donk',
        brand = 'Willard',
        model = 'faction3',
        price = 58971,
        category = 'muscle',
        type = 'automobile',
        hash = `faction3`,
    },
    fagaloa = {
        name = 'Fagaloa',
        brand = 'Vulcar',
        model = 'fagaloa',
        price = 57546,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `fagaloa`,
    },
    faggio = {
        name = 'Faggio Sport',
        brand = 'Pegassi',
        model = 'faggio',
        price = 14728,
        category = 'motorcycles',
        type = 'bike',
        hash = `faggio`,
    },
    faggio2 = {
        name = 'Faggio Sport',
        brand = 'Pegassi',
        model = 'faggio2',
        price = 12263,
        category = 'motorcycles',
        type = 'bike',
        hash = `faggio2`,
    },
    faggio3 = {
        name = 'Faggio Mod',
        brand = 'Pegassi',
        model = 'faggio3',
        price = 14291,
        category = 'motorcycles',
        type = 'bike',
        hash = `faggio3`,
    },
    fbi = {
        name = 'FIB',
        brand = 'Bravado',
        model = 'fbi',
        price = 76794,
        category = 'emergency',
        type = 'automobile',
        hash = `fbi`,
    },
    fbi2 = {
        name = 'FIB',
        brand = 'Declasse',
        model = 'fbi2',
        price = 62617,
        category = 'emergency',
        type = 'automobile',
        hash = `fbi2`,
    },
    fcr = {
        name = 'FCR 1000',
        brand = 'Pegassi',
        model = 'fcr',
        price = 24321,
        category = 'motorcycles',
        type = 'bike',
        hash = `fcr`,
    },
    fcr2 = {
        name = 'FCR 1000 Custom',
        brand = 'Pegassi',
        model = 'fcr2',
        price = 24491,
        category = 'motorcycles',
        type = 'bike',
        hash = `fcr2`,
    },
    felon = {
        name = 'Felon',
        brand = 'Lampadati',
        model = 'felon',
        price = 76088,
        category = 'coupes',
        type = 'automobile',
        hash = `felon`,
    },
    felon2 = {
        name = 'Felon GT',
        brand = 'Lampadati',
        model = 'felon2',
        price = 72343,
        category = 'coupes',
        type = 'automobile',
        hash = `felon2`,
    },
    feltzer2 = {
        name = 'Feltzer',
        brand = 'Benefactor',
        model = 'feltzer2',
        price = 82235,
        category = 'sports',
        type = 'automobile',
        hash = `feltzer2`,
    },
    feltzer3 = {
        name = 'Stirling GT',
        brand = 'Benefactor',
        model = 'feltzer3',
        price = 76445,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `feltzer3`,
    },
    firebolt = {
        name = 'Firebolt ASP',
        brand = 'Vapid',
        model = 'firebolt',
        price = 64793,
        category = 'emergency',
        type = 'automobile',
        hash = `firebolt`,
    },
    firetruk = {
        name = 'Fire Truck',
        brand = 'MTL',
        model = 'firetruk',
        price = 64793,
        category = 'emergency',
        type = 'automobile',
        hash = `firetruk`,
    },
    fixter = {
        name = 'Fixter',
        brand = '',
        model = 'fixter',
        price = 2835,
        category = 'cycles',
        type = 'bike',
        hash = `fixter`,
    },
    flashgt = {
        name = 'Flash GT',
        brand = 'Vapid',
        model = 'flashgt',
        price = 73890,
        category = 'sports',
        type = 'automobile',
        hash = `flashgt`,
    },
    flatbed = {
        name = 'Flatbed',
        brand = 'MTL',
        model = 'flatbed',
        price = 46633,
        category = 'industrial',
        type = 'automobile',
        hash = `flatbed`,
    },
    fmj = {
        name = 'FMJ',
        brand = 'Vapid',
        model = 'fmj',
        price = 88017,
        category = 'super',
        type = 'automobile',
        hash = `fmj`,
    },
    forklift = {
        name = 'Forklift',
        brand = 'HVY',
        model = 'forklift',
        price = 17664,
        category = 'utility',
        type = 'automobile',
        hash = `forklift`,
    },
    formula = {
        name = 'PR4',
        brand = 'Progen',
        model = 'formula',
        price = 106933,
        category = 'openwheel',
        type = 'automobile',
        hash = `formula`,
    },
    formula2 = {
        name = 'R88',
        brand = 'Ocelot',
        model = 'formula2',
        price = 106571,
        category = 'openwheel',
        type = 'automobile',
        hash = `formula2`,
    },
    fq2 = {
        name = 'FQ2',
        brand = 'Fathom',
        model = 'fq2',
        price = 65442,
        category = 'suvs',
        type = 'automobile',
        hash = `fq2`,
    },
    fr36 = {
        name = 'FR36',
        brand = 'Fathom',
        model = 'fr36',
        price = 78667,
        category = 'coupes',
        type = 'automobile',
        hash = `fr36`,
    },
    freecrawler = {
        name = 'Freecrawler',
        brand = 'Canis',
        model = 'freecrawler',
        price = 60453,
        category = 'offroad',
        type = 'automobile',
        hash = `freecrawler`,
    },
    freight = {
        name = 'Freight Train (Locomotive)',
        brand = '',
        model = 'freight',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freight`,
    },
    freight2 = {
        name = 'Freight Train (Chop Shop)',
        brand = '',
        model = 'freight2',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freight2`,
    },
    freightcar = {
        name = 'Freight Train (Container)',
        brand = '',
        model = 'freightcar',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightcar`,
    },
    freightcar2 = {
        name = 'Freight Train (Flatbed Trailer)',
        brand = '',
        model = 'freightcar2',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightcar2`,
    },
    freightcar3 = {
        name = 'Freight Train (Flatbed Trailer)',
        brand = '',
        model = 'freightcar3',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightcar3`,
    },
    freightcont1 = {
        name = 'Freight Train (Container)',
        brand = '',
        model = 'freightcont1',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightcont1`,
    },
    freightcont2 = {
        name = 'Freight Train (Lando Container)',
        brand = '',
        model = 'freightcont2',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightcont2`,
    },
    freightgrain = {
        name = 'Freight Train (Grain Trailer)',
        brand = '',
        model = 'freightgrain',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `freightgrain`,
    },
    freighttrailer = {
        name = 'Army Trailer',
        brand = '',
        model = 'freighttrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `freighttrailer`,
    },
    frogger = {
        name = 'Frogger',
        brand = 'Maibatsu',
        model = 'frogger',
        price = 6367865,
        category = 'helicopters',
        type = 'heli',
        hash = `frogger`,
    },
    frogger2 = {
        name = 'Frogger (Trevor Philips Enterprises)',
        brand = 'Maibatsu',
        model = 'frogger2',
        price = 6367865,
        category = 'helicopters',
        type = 'heli',
        hash = `frogger2`,
    },
    fugitive = {
        name = 'Fugitive',
        brand = 'Cheval',
        model = 'fugitive',
        price = 67317,
        category = 'sedans',
        type = 'automobile',
        hash = `fugitive`,
    },
    furia = {
        name = 'Furia',
        brand = 'Grotti',
        model = 'furia',
        price = 83110,
        category = 'super',
        type = 'automobile',
        hash = `furia`,
    },
    furoregt = {
        name = 'Furore GT',
        brand = 'Lampadati',
        model = 'furoregt',
        price = 84346,
        category = 'sports',
        type = 'automobile',
        hash = `furoregt`,
    },
    fusilade = {
        name = 'Fusilade',
        brand = 'Schyster',
        model = 'fusilade',
        price = 81251,
        category = 'sports',
        type = 'automobile',
        hash = `fusilade`,
    },
    futo = {
        name = 'Futo',
        brand = 'Karin',
        model = 'futo',
        price = 74336,
        category = 'sports',
        type = 'automobile',
        hash = `futo`,
    },
    futo2 = {
        name = 'Futo GTX',
        brand = 'Karin',
        model = 'futo2',
        price = 75463,
        category = 'sports',
        type = 'automobile',
        hash = `futo2`,
    },
    gargoyle = {
        name = 'Gargoyle',
        brand = 'Western',
        model = 'gargoyle',
        price = 25195,
        category = 'motorcycles',
        type = 'bike',
        hash = `gargoyle`,
    },
    gauntlet = {
        name = 'Gauntlet',
        brand = 'Bravado',
        model = 'gauntlet',
        price = 78237,
        category = 'muscle',
        type = 'automobile',
        hash = `gauntlet`,
    },
    gauntlet2 = {
        name = 'Redwood Gauntlet',
        brand = 'Bravado',
        model = 'gauntlet2',
        price = 80506,
        category = 'muscle',
        type = 'automobile',
        hash = `gauntlet2`,
    },
    gauntlet3 = {
        name = 'Gauntlet Classic',
        brand = 'Bravado',
        model = 'gauntlet3',
        price = 73572,
        category = 'muscle',
        type = 'automobile',
        hash = `gauntlet3`,
    },
    gauntlet4 = {
        name = 'Gauntlet Hellfire',
        brand = 'Bravado',
        model = 'gauntlet4',
        price = 81048,
        category = 'muscle',
        type = 'automobile',
        hash = `gauntlet4`,
    },
    gauntlet5 = {
        name = 'Gauntlet Classic Custom',
        brand = 'Bravado',
        model = 'gauntlet5',
        price = 80250,
        category = 'muscle',
        type = 'automobile',
        hash = `gauntlet5`,
    },
    gauntlet6 = {
        name = 'Hotring Hellfire',
        brand = 'Bravado',
        model = 'gauntlet6',
        price = 81665,
        category = 'sports',
        type = 'automobile',
        hash = `gauntlet6`,
    },
    gb200 = {
        name = 'GB 200',
        brand = 'Vapid',
        model = 'gb200',
        price = 74534,
        category = 'sports',
        type = 'automobile',
        hash = `gb200`,
    },
    gburrito = {
        name = 'Gang Burrito (Lost MC)',
        brand = 'Declasse',
        model = 'gburrito',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `gburrito`,
    },
    gburrito2 = {
        name = 'Burrito Custom',
        brand = 'Declasse',
        model = 'gburrito2',
        price = 66162,
        category = 'vans',
        type = 'automobile',
        hash = `gburrito2`,
    },
    glendale = {
        name = 'Glendale',
        brand = 'Benefactor',
        model = 'glendale',
        price = 65948,
        category = 'sedans',
        type = 'automobile',
        hash = `glendale`,
    },
    glendale2 = {
        name = 'Glendale Custom',
        brand = 'Benefactor',
        model = 'glendale2',
        price = 66105,
        category = 'sedans',
        type = 'automobile',
        hash = `glendale2`,
    },
    gp1 = {
        name = 'GP1',
        brand = 'Progen',
        model = 'gp1',
        price = 84220,
        category = 'super',
        type = 'automobile',
        hash = `gp1`,
    },
    graintrailer = {
        name = 'Grain Trailer',
        brand = '',
        model = 'graintrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `graintrailer`,
    },
    granger = {
        name = 'Granger',
        brand = 'Declasse',
        model = 'granger',
        price = 60692,
        category = 'suvs',
        type = 'automobile',
        hash = `granger`,
    },
    granger2 = {
        name = 'Granger 3600LX',
        brand = 'Declasse',
        model = 'granger2',
        price = 57057,
        category = 'suvs',
        type = 'automobile',
        hash = `granger2`,
    },
    greenwood = {
        name = 'Greenwood',
        brand = 'Bravado',
        model = 'greenwood',
        price = 78897,
        category = 'muscle',
        type = 'automobile',
        hash = `greenwood`,
    },
    gresley = {
        name = 'Gresley',
        brand = 'Bravado',
        model = 'gresley',
        price = 63694,
        category = 'suvs',
        type = 'automobile',
        hash = `gresley`,
    },
    growler = {
        name = 'Growler',
        brand = 'Pfister',
        model = 'growler',
        price = 82635,
        category = 'sports',
        type = 'automobile',
        hash = `growler`,
    },
    gt500 = {
        name = 'GT500',
        brand = 'Grotti',
        model = 'gt500',
        price = 76158,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `gt500`,
    },
    guardian = {
        name = 'Guardian',
        brand = 'Vapid',
        model = 'guardian',
        price = 65628,
        category = 'industrial',
        type = 'automobile',
        hash = `guardian`,
    },
    habanero = {
        name = 'Habanero',
        brand = 'Emperor',
        model = 'habanero',
        price = 65442,
        category = 'suvs',
        type = 'automobile',
        hash = `habanero`,
    },
    hakuchou = {
        name = 'Hakuchou',
        brand = 'Shitzu',
        model = 'hakuchou',
        price = 25975,
        category = 'motorcycles',
        type = 'bike',
        hash = `hakuchou`,
    },
    hakuchou2 = {
        name = 'Hakuchou Drag',
        brand = 'Shitzu',
        model = 'hakuchou2',
        price = 27374,
        category = 'motorcycles',
        type = 'bike',
        hash = `hakuchou2`,
    },
    halftrack = {
        name = 'Half-track',
        brand = 'Bravado',
        model = 'halftrack',
        price = 34933,
        category = 'military',
        type = 'automobile',
        hash = `halftrack`,
    },
    handler = {
        name = 'Dock Handler',
        brand = 'HVY',
        model = 'handler',
        price = 14549,
        category = 'industrial',
        type = 'automobile',
        hash = `handler`,
    },
    hauler = {
        name = 'Hauler',
        brand = 'JoBuilt',
        model = 'hauler',
        price = 48240,
        category = 'commercial',
        type = 'automobile',
        hash = `hauler`,
    },
    hauler2 = {
        name = 'Hauler Custom',
        brand = 'JoBuilt',
        model = 'hauler2',
        price = 70102,
        category = 'commercial',
        type = 'automobile',
        hash = `hauler2`,
    },
    havok = {
        name = 'Havok',
        brand = 'Nagasaki',
        model = 'havok',
        price = 6462758,
        category = 'helicopters',
        type = 'heli',
        hash = `havok`,
    },
    hellion = {
        name = 'Hellion',
        brand = 'Annis',
        model = 'hellion',
        price = 66525,
        category = 'offroad',
        type = 'automobile',
        hash = `hellion`,
    },
    hermes = {
        name = 'Hermes',
        brand = 'Albany',
        model = 'hermes',
        price = 68776,
        category = 'muscle',
        type = 'automobile',
        hash = `hermes`,
    },
    hexer = {
        name = 'Hexer',
        brand = 'LCC',
        model = 'hexer',
        price = 22521,
        category = 'motorcycles',
        type = 'bike',
        hash = `hexer`,
    },
    hotknife = {
        brand = 'Vapid',
        name = 'Hotknife',
        model = 'hotknife',
        price = 74328,
        category = 'muscle',
        type = 'automobile',hash = `hotknife`,

    },
    hotring = {
        name = 'Hotring Sabre',
        brand = 'Declasse',
        model = 'hotring',
        price = 79724,
        category = 'sports',
        type = 'automobile',
        hash = `hotring`,
    },
    howard = {
        name = 'Howard NX-25',
        brand = 'Buckingham',
        model = 'howard',
        price = 2587839,
        category = 'planes',
        type = 'plane',
        hash = `howard`,
    },
    hunter = {
        name = 'FH-1 Hunter',
        brand = '',
        model = 'hunter',
        price = 6546934,
        category = 'helicopters',
        type = 'heli',
        hash = `hunter`,
    },
    huntley = {
        name = 'Huntley S',
        brand = 'Enus',
        model = 'huntley',
        price = 74869,
        category = 'suvs',
        type = 'automobile',
        hash = `huntley`,
    },
    hustler = {
        name = 'Hustler',
        brand = 'Vapid',
        model = 'hustler',
        price = 72900,
        category = 'muscle',
        type = 'automobile',
        hash = `hustler`,
    },
    hydra = {
        name = 'Hydra',
        brand = 'Mammoth',
        model = 'hydra',
        price = 2193367,
        category = 'planes',
        type = 'plane',
        hash = `hydra`,
    },
    ignus = {
        name = 'Ignus',
        brand = 'Pegassi',
        model = 'ignus',
        price = 87219,
        category = 'super',
        type = 'automobile',
        hash = `ignus`,
    },
    imorgon = {
        name = 'Imorgon',
        brand = 'Överflöd',
        model = 'imorgon',
        price = 76854,
        category = 'sports',
        type = 'automobile',
        hash = `imorgon`,
    },
    impaler = {
        name = 'Impaler',
        brand = 'Declasse',
        model = 'impaler',
        price = 74584,
        category = 'muscle',
        type = 'automobile',
        hash = `impaler`,
    },
    impaler2 = {
        name = 'Apocalypse Impaler',
        brand = 'Declasse',
        model = 'impaler2',
        price = 84076,
        category = 'muscle',
        type = 'automobile',
        hash = `impaler2`,
    },
    impaler3 = {
        name = 'Future Shock Impaler',
        brand = 'Declasse',
        model = 'impaler3',
        price = 84076,
        category = 'muscle',
        type = 'automobile',
        hash = `impaler3`,
    },
    impaler4 = {
        name = 'Nightmare Impaler',
        brand = 'Declasse',
        model = 'impaler4',
        price = 84076,
        category = 'muscle',
        type = 'automobile',
        hash = `impaler4`,
    },
    impaler5 = {
        name = 'Impaler SZ',
        brand = 'Declasse',
        model = 'impaler5',
        price = 78875,
        category = 'sedans',
        type = 'automobile',
        hash = `impaler5`,
    },
    impaler6 = {
        name = 'Impaler LX',
        brand = 'Declasse',
        model = 'impaler6',
        price = 77917,
        category = 'muscle',
        type = 'automobile',
        hash = `impaler6`,
    },
    imperator = {
        name = 'Apocalypse Imperator',
        brand = 'Vapid',
        model = 'imperator',
        price = 80296,
        category = 'muscle',
        type = 'automobile',
        hash = `imperator`,
    },
    imperator2 = {
        name = 'Future Shock Imperator',
        brand = 'Vapid',
        model = 'imperator2',
        price = 80296,
        category = 'muscle',
        type = 'automobile',
        hash = `imperator2`,
    },
    imperator3 = {
        name = 'Nightmare Imperator',
        brand = 'Vapid',
        model = 'imperator3',
        price = 80296,
        category = 'muscle',
        type = 'automobile',
        hash = `imperator3`,
    },
    inductor = {
        name = 'Inductor',
        brand = 'Coil',
        model = 'inductor',
        price = 2607,
        category = 'cycles',
        type = 'bike',
        hash = `inductor`,
    },
    inductor2 = {
        name = 'Junk Energy Inductor',
        brand = 'Coil',
        model = 'inductor2',
        price = 2607,
        category = 'cycles',
        type = 'bike',
        hash = `inductor2`,
    },
    infernus = {
        name = 'Infernus',
        brand = 'Pegassi',
        model = 'infernus',
        price = 81077,
        category = 'super',
        type = 'automobile',
        hash = `infernus`,
    },
    infernus2 = {
        name = 'Infernus Classic',
        brand = 'Pegassi',
        model = 'infernus2',
        price = 79349,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `infernus2`,
    },
    ingot = {
        name = 'Ingot',
        brand = 'Vulcar',
        model = 'ingot',
        price = 53344,
        category = 'sedans',
        type = 'automobile',
        hash = `ingot`,
    },
    innovation = {
        name = 'Innovation',
        brand = 'LCC',
        model = 'innovation',
        price = 23509,
        category = 'motorcycles',
        type = 'bike',
        hash = `innovation`,
    },
    insurgent = {
        name = 'Insurgent Pick-Up',
        brand = 'HVY',
        model = 'insurgent',
        price = 59045,
        category = 'offroad',
        type = 'automobile',
        hash = `insurgent`,
    },
    insurgent2 = {
        name = 'Insurgent',
        brand = 'HVY',
        model = 'insurgent2',
        price = 59045,
        category = 'offroad',
        type = 'automobile',
        hash = `insurgent2`,
    },
    insurgent3 = {
        name = 'Insurgent Pick-Up Custom',
        brand = 'HVY',
        model = 'insurgent3',
        price = 59045,
        category = 'offroad',
        type = 'automobile',
        hash = `insurgent3`,
    },
    intruder = {
        name = 'Intruder',
        brand = 'Karin',
        model = 'intruder',
        price = 65682,
        category = 'sedans',
        type = 'automobile',
        hash = `intruder`,
    },
    issi2 = {
        name = 'Issi',
        brand = 'Weeny',
        model = 'issi2',
        price = 69371,
        category = 'compacts',
        type = 'automobile',
        hash = `issi2`,
    },
    issi3 = {
        name = 'Issi Classic',
        brand = 'Weeny',
        model = 'issi3',
        price = 65593,
        category = 'compacts',
        type = 'automobile',
        hash = `issi3`,
    },
    issi4 = {
        name = 'Apocalypse Issi',
        brand = 'Weeny',
        model = 'issi4',
        price = 74235,
        category = 'compacts',
        type = 'automobile',
        hash = `issi4`,
    },
    issi5 = {
        name = 'Future Shock Issi',
        brand = 'Weeny',
        model = 'issi5',
        price = 74235,
        category = 'compacts',
        type = 'automobile',
        hash = `issi5`,
    },
    issi6 = {
        name = 'Nightmare Issi',
        brand = 'Weeny',
        model = 'issi6',
        price = 74235,
        category = 'compacts',
        type = 'automobile',
        hash = `issi6`,
    },
    issi7 = {
        name = 'Issi Sport',
        brand = 'Weeny',
        model = 'issi7',
        price = 70443,
        category = 'sports',
        type = 'automobile',
        hash = `issi7`,
    },
    issi8 = {
        name = 'Issi Rally',
        brand = 'Weeny',
        model = 'issi8',
        price = 79711,
        category = 'suvs',
        type = 'automobile',
        hash = `issi8`,
    },
    italigtb = {
        name = 'Itali GTB',
        brand = 'Progen',
        model = 'italigtb',
        price = 86900,
        category = 'super',
        type = 'automobile',
        hash = `italigtb`,
    },
    italigtb2 = {
        name = 'Itali GTB Custom',
        brand = 'Progen',
        model = 'italigtb2',
        price = 87587,
        category = 'super',
        type = 'automobile',
        hash = `italigtb2`,
    },
    italigto = {
        name = 'Itali GTO',
        brand = 'Grotti',
        model = 'italigto',
        price = 87475,
        category = 'sports',
        type = 'automobile',
        hash = `italigto`,
    },
    italirsx = {
        name = 'Itali RSX',
        brand = 'Grotti',
        model = 'italirsx',
        price = 87682,
        category = 'sports',
        type = 'automobile',
        hash = `italirsx`,
    },
    iwagen = {
        brand = 'Obey',
        name = 'I-Wagen',
        model = 'iwagen',
        price = 55888,
        category = 'suvs',
        type = 'automobile',
        hash = `iwagen`,
    },
    jackal = {
        name = 'Jackal',
        brand = 'Ocelot',
        model = 'jackal',
        price = 76324,
        category = 'coupes',
        type = 'automobile',
        hash = `jackal`,
    },
    jb700 = {
        name = 'JB 700',
        brand = 'Dewbauchee',
        model = 'jb700',
        price = 82400,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `jb700`,
    },
    jb7002 = {
        name = 'JB 700W',
        brand = 'Dewbauchee',
        model = 'jb7002',
        price = 82400,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `jb7002`,
    },
    jester = {
        name = 'Jester',
        brand = 'Dinka',
        model = 'jester',
        price = 76685,
        category = 'sports',
        type = 'automobile',
        hash = `jester`,
    },
    jester2 = {
        name = 'Jester (Racecar)',
        brand = 'Dinka',
        model = 'jester2',
        price = 78165,
        category = 'sports',
        type = 'automobile',
        hash = `jester2`,
    },
    jester3 = {
        name = 'Jester Classic',
        brand = 'Dinka',
        model = 'jester3',
        price = 80462,
        category = 'sports',
        type = 'automobile',
        hash = `jester3`,
    },
    jester4 = {
        name = 'Jester RR',
        brand = 'Dinka',
        model = 'jester4',
        price = 79879,
        category = 'sports',
        type = 'automobile',
        hash = `jester4`,
    },
    jester5 = {
        name = 'Jester RR Widebody',
        brand = 'Dinka',
        model = 'jester5',
        price = 79879,
        category = 'sports',
        type = 'automobile',
        hash = `jester5`,
    },
    jet = {
        name = 'Jet',
        brand = '',
        model = 'jet',
        price = 1537948,
        category = 'planes',
        type = 'plane',
        hash = `jet`,
    },
    jetmax = {
        name = 'Jetmax',
        brand = 'Shitzu',
        model = 'jetmax',
        price = 478680,
        category = 'boats',
        type = 'boat',
        hash = `jetmax`,
    },
    journey = {
        name = 'Journey',
        brand = 'Zirconium',
        model = 'journey',
        price = 52886,
        category = 'vans',
        type = 'automobile',
        hash = `journey`,
    },
    journey2 = {
        name = 'Journey II',
        brand = 'Zirconium',
        model = 'journey2',
        price = 52886,
        category = 'vans',
        type = 'automobile',
        hash = `journey2`,
    },
    jubilee = {
        name = 'Jubilee',
        brand = 'Enus',
        model = 'jubilee',
        price = 71210,
        category = 'suvs',
        type = 'automobile',
        hash = `jubilee`,
    },
    jugular = {
        name = 'Jugular',
        brand = 'Ocelot',
        model = 'jugular',
        price = 81412,
        category = 'sports',
        type = 'automobile',
        hash = `jugular`,
    },
    kalahari = {
        name = 'Kalahari',
        brand = 'Canis',
        model = 'kalahari',
        price = 59046,
        category = 'offroad',
        type = 'automobile',
        hash = `kalahari`,
    },
    kamacho = {
        name = 'Kamacho',
        brand = 'Canis',
        model = 'kamacho',
        price = 67315,
        category = 'offroad',
        type = 'automobile',
        hash = `kamacho`,
    },
    kanjo = {
        name = 'Blista Kanjo',
        brand = 'Dinka',
        model = 'kanjo',
        price = 73478,
        category = 'compacts',
        type = 'automobile',
        hash = `kanjo`,
    },
    kanjosj = {
        name = 'Kanjo SJ',
        brand = 'Dinka',
        model = 'kanjosj',
        price = 73789,
        category = 'coupes',
        type = 'automobile',
        hash = `kanjosj`,
    },
    khamelion = {
        name = 'Khamelion',
        brand = 'Hijak',
        model = 'khamelion',
        price = 53292,
        category = 'sports',
        type = 'automobile',
        hash = `khamelion`,
    },
    khanjali = {
        name = 'TM-02 Khanjali',
        brand = '',
        model = 'khanjali',
        price = 30243,
        category = 'military',
        type = 'automobile',
        hash = `khanjali`,
    },
    komoda = {
        name = 'Komoda',
        brand = 'Lampadati',
        model = 'komoda',
        price = 83129,
        category = 'sports',
        type = 'automobile',
        hash = `komoda`,
    },
    kosatka = {
        name = 'Kosatka',
        brand = 'RUNE',
        model = 'kosatka',
        price = 624016,
        category = 'boats',
        type = 'boat',
        hash = `kosatka`,
    },
    krieger = {
        name = 'Krieger',
        brand = 'Benefactor',
        model = 'krieger',
        price = 87715,
        category = 'super',
        type = 'automobile',
        hash = `krieger`,
    },
    kuruma = {
        name = 'Kuruma',
        brand = 'Karin',
        model = 'kuruma',
        price = 77445,
        category = 'sports',
        type = 'automobile',
        hash = `kuruma`,
    },
    kuruma2 = {
        name = 'Kuruma (Armored)',
        brand = 'Karin',
        model = 'kuruma2',
        price = 75885,
        category = 'sports',
        type = 'automobile',
        hash = `kuruma2`,
    },
    l35 = {
        name = 'Walton L35',
        brand = 'Declasse',
        model = 'l35',
        price = 64429,
        category = 'offroad',
        type = 'automobile',
        hash = `l35`,
    },
    landstalker = {
        name = 'Landstalker',
        brand = 'Dundreary',
        model = 'landstalker',
        price = 61393,
        category = 'suvs',
        type = 'automobile',
        hash = `landstalker`,
    },
    landstalker2 = {
        name = 'Landstalker XL',
        brand = 'Dundreary',
        model = 'landstalker2',
        price = 62744,
        category = 'suvs',
        type = 'automobile',
        hash = `landstalker2`,
    },
    lazer = {
        name = 'P-996 LAZER',
        brand = 'Jobuilt',
        model = 'lazer',
        price = 2374069,
        category = 'planes',
        type = 'plane',
        hash = `lazer`,
    },
    le7b = {
        name = 'RE-7B',
        brand = 'Annis',
        model = 'le7b',
        price = 84125,
        category = 'super',
        type = 'automobile',
        hash = `le7b`,
    },
    lectro = {
        name = 'Lectro',
        brand = 'Principe',
        model = 'lectro',
        price = 27225,
        category = 'motorcycles',
        type = 'bike',
        hash = `lectro`,
    },
    lguard = {
        name = 'Lifeguard',
        brand = 'Declasse',
        model = 'lguard',
        price = 62617,
        category = 'emergency',
        type = 'automobile',
        hash = `lguard`,
    },
    limo2 = {
        name = 'Turreted Limo',
        brand = 'Benefactor',
        model = 'limo2',
        price = 65584,
        category = 'sedans',
        type = 'automobile',
        hash = `limo2`,
    },
    lm87 = {
        name = 'LM87',
        brand = 'Benefactor',
        model = 'lm87',
        price = 84509,
        category = 'super',
        type = 'automobile',
        hash = `lm87`,
    },
    locust = {
        name = 'Locust',
        brand = 'Ocelot',
        model = 'locust',
        price = 81371,
        category = 'sports',
        type = 'automobile',
        hash = `locust`,
    },
    longfin = {
        name = 'Longfin',
        brand = 'Shitzu',
        model = 'longfin',
        price = 500680,
        category = 'boats',
        type = 'boat',
        hash = `longfin`,
    },
    lurcher = {
        name = 'Lurcher',
        brand = 'Albany',
        model = 'lurcher',
        price = 78245,
        category = 'muscle',
        type = 'automobile',
        hash = `lurcher`,
    },
    luxor = {
        name = 'Luxor',
        brand = 'Buckingham',
        model = 'luxor',
        price = 1818934,
        category = 'planes',
        type = 'plane',
        hash = `luxor`,
    },
    luxor2 = {
        name = 'Luxor Deluxe',
        brand = 'Buckingham',
        model = 'luxor2',
        price = 1834048,
        category = 'planes',
        type = 'plane',
        hash = `luxor2`,
    },
    lynx = {
        name = 'Lynx',
        brand = 'Ocelot',
        model = 'lynx',
        price = 81739,
        category = 'sports',
        type = 'automobile',
        hash = `lynx`,
    },
    mamba = {
        name = 'Mamba',
        brand = 'Declasse',
        model = 'mamba',
        price = 81429,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `mamba`,
    },
    mammatus = {
        name = 'Mammatus',
        brand = 'JoBuilt',
        model = 'mammatus',
        price = 1321065,
        category = 'planes',
        type = 'plane',
        hash = `mammatus`,
    },
    manana = {
        name = 'Manana',
        brand = 'Albany',
        model = 'manana',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `manana`,
    },
    manana2 = {
        name = 'Manana Custom',
        brand = 'Albany',
        model = 'manana2',
        price = 71516,
        category = 'muscle',
        type = 'automobile',
        hash = `manana2`,
    },
    manchez = {
        name = 'Manchez',
        brand = 'Maibatsu',
        model = 'manchez',
        price = 23138,
        category = 'motorcycles',
        type = 'bike',
        hash = `manchez`,
    },
    manchez2 = {
        name = 'Manchez Scout',
        brand = 'Maibatsu',
        model = 'manchez2',
        price = 21545,
        category = 'motorcycles',
        type = 'bike',
        hash = `manchez2`,
    },
    manchez3 = {
        name = 'Manchez Scout C',
        brand = 'Maibatsu',
        model = 'manchez3',
        price = 21632,
        category = 'motorcycles',
        type = 'bike',
        hash = `manchez3`,
    },
    marquis = {
        name = 'Marquis',
        brand = 'Dinka',
        model = 'marquis',
        price = 94680,
        category = 'boats',
        type = 'boat',
        hash = `marquis`,
    },
    marshall = {
        name = 'Marshall',
        brand = 'Cheval',
        model = 'marshall',
        price = 57276,
        category = 'offroad',
        type = 'automobile',
        hash = `marshall`,
    },
    massacro = {
        name = 'Massacro',
        brand = 'Dewbauchee',
        model = 'massacro',
        price = 83972,
        category = 'sports',
        type = 'automobile',
        hash = `massacro`,
    },
    massacro2 = {
        name = 'Massacro Racecar',
        brand = 'Dewbauchee',
        model = 'massacro2',
        price = 83972,
        category = 'sports',
        type = 'automobile',
        hash = `massacro2`,
    },
    maverick = {
        name = 'Maverick',
        brand = 'Western',
        model = 'maverick',
        price = 6031333,
        category = 'helicopters',
        type = 'heli',
        hash = `maverick`,
    },
    menacer = {
        name = 'Menacer',
        brand = 'HVY',
        model = 'menacer',
        price = 53078,
        category = 'offroad',
        type = 'automobile',
        hash = `menacer`,
    },
    mesa = {
        name = 'Mesa',
        brand = 'Canis',
        model = 'mesa',
        price = 57125,
        category = 'suvs',
        type = 'automobile',
        hash = `mesa`,
    },
    mesa2 = {
        name = 'Mesa (Snow)',
        brand = 'Canis',
        model = 'mesa2',
        price = 57125,
        category = 'suvs',
        type = 'automobile',
        hash = `mesa2`,
    },
    mesa3 = {
        name = 'Mesa (Merryweather)',
        brand = 'Canis',
        model = 'mesa3',
        price = 57125,
        category = 'offroad',
        type = 'automobile',
        hash = `mesa3`,
    },
    metrotrain = {
        name = 'Freight Train (Tram)',
        brand = '',
        model = 'metrotrain',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `metrotrain`,
    },
    michelli = {
        name = 'Michelli GT',
        brand = 'Lampadati',
        model = 'michelli',
        price = 74020,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `michelli`,
    },
    microlight = {
        name = 'Ultralight',
        brand = 'Nagasaki',
        model = 'microlight',
        price = 987645,
        category = 'planes',
        type = 'plane',
        hash = `microlight`,
    },
    miljet = {
        name = 'Miljet',
        brand = 'Buckingham',
        model = 'miljet',
        price = 1849116,
        category = 'planes',
        type = 'plane',
        hash = `miljet`,
    },
    minitank = {
        name = 'Invade and Persuade Tank',
        brand = '',
        model = 'minitank',
        price = 24911,
        category = 'military',
        type = 'automobile',
        hash = `minitank`,
    },
    minivan = {
        name = 'Minivan',
        brand = 'Vapid',
        model = 'minivan',
        price = 58370,
        category = 'vans',
        type = 'automobile',
        hash = `minivan`,
    },
    minivan2 = {
        name = 'Minivan Custom',
        brand = 'Vapid',
        model = 'minivan2',
        price = 58450,
        category = 'vans',
        type = 'automobile',
        hash = `minivan2`,
    },
    mixer = {
        brand = 'HVY',
        category = 'industrial',
        hash = `mixer`,
        model = 'mixer',
        name = 'Mixer',
        type = 'automobile',
        price = 53283,
    },
    mixer2 = {
        name = 'Mixer',
        brand = 'HVY',
        model = 'mixer2',
        price = 53283,
        category = 'industrial',
        type = 'automobile',
        hash = `mixer2`,
    },
    mogul = {
        name = 'Mogul',
        brand = 'Mammoth',
        model = 'mogul',
        price = 1489140,
        category = 'planes',
        type = 'plane',
        hash = `mogul`,
    },
    molotok = {
        name = 'V-65 Molotok',
        brand = '',
        model = 'molotok',
        price = 2145700,
        category = 'planes',
        type = 'plane',
        hash = `molotok`,
    },
    monroe = {
        name = 'Monroe',
        brand = 'Pegassi',
        model = 'monroe',
        price = 82544,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `monroe`,
    },
    monster = {
        name = 'Liberator',
        brand = 'Vapid',
        model = 'monster',
        price = 57276,
        category = 'offroad',
        type = 'automobile',
        hash = `monster`,
    },
    monster3 = {
        name = 'Apocalypse Sasquatch',
        brand = 'Bravado',
        model = 'monster3',
        price = 64365,
        category = 'offroad',
        type = 'automobile',
        hash = `monster3`,
    },
    monster4 = {
        name = 'Future Shock Sasquatch',
        brand = 'Bravado',
        model = 'monster4',
        price = 64365,
        category = 'offroad',
        type = 'automobile',
        hash = `monster4`,
    },
    monster5 = {
        name = 'Nightmare Sasquatch',
        brand = 'Bravado',
        model = 'monster5',
        price = 64365,
        category = 'offroad',
        type = 'automobile',
        hash = `monster5`,
    },
    monstrociti = {
        name = 'MonstroCiti',
        brand = 'Maibatsu',
        model = 'monstrociti',
        price = 68406,
        category = 'offroad',
        type = 'automobile',
        hash = `monstrociti`,
    },
    moonbeam = {
        name = 'Moonbeam',
        brand = 'Declasse',
        model = 'moonbeam',
        price = 68586,
        category = 'muscle',
        type = 'automobile',
        hash = `moonbeam`,
    },
    moonbeam2 = {
        name = 'Moonbeam Custom',
        brand = 'Declasse',
        model = 'moonbeam2',
        price = 68586,
        category = 'muscle',
        type = 'automobile',
        hash = `moonbeam2`,
    },
    mower = {
        name = 'Lawn Mower',
        brand = 'Jack Sheepe',
        model = 'mower',
        price = 12234,
        category = 'utility',
        type = 'automobile',
        hash = `mower`,
    },
    mule = {
        name = 'Mule',
        brand = 'Maibatsu',
        model = 'mule',
        price = 47273,
        category = 'commercial',
        type = 'automobile',
        hash = `mule`,
    },
    mule2 = {
        name = 'Mule (Ramp Door)',
        brand = 'Maibatsu',
        model = 'mule2',
        price = 47273,
        category = 'commercial',
        type = 'automobile',
        hash = `mule2`,
    },
    mule3 = {
        name = 'Mule (Heist)',
        brand = 'Maibatsu',
        model = 'mule3',
        price = 52599,
        category = 'commercial',
        type = 'automobile',
        hash = `mule3`,
    },
    mule4 = {
        name = 'Mule Custom',
        brand = 'Maibatsu',
        model = 'mule4',
        price = 47273,
        category = 'commercial',
        type = 'automobile',
        hash = `mule4`,
    },
    mule5 = {
        name = 'Box Truck Mule',
        brand = 'Maibatsu',
        model = 'mule5',
        price = 52599,
        category = 'commercial',
        type = 'automobile',
        hash = `mule5`,
    },
    nebula = {
        name = 'Nebula Turbo',
        brand = 'Vulcar',
        model = 'nebula',
        price = 62877,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `nebula`,
    },
    nemesis = {
        name = 'Nemesis',
        brand = 'Principe',
        model = 'nemesis',
        price = 23612,
        category = 'motorcycles',
        type = 'bike',
        hash = `nemesis`,
    },
    neo = {
        name = 'Neo',
        brand = 'Vysser',
        model = 'neo',
        price = 85696,
        category = 'sports',
        type = 'automobile',
        hash = `neo`,
    },
    neon = {
        name = 'Neon',
        brand = 'Pfister',
        model = 'neon',
        price = 65765,
        category = 'sports',
        type = 'automobile',
        hash = `neon`,
    },
    nero = {
        name = 'Nero',
        brand = 'Truffade',
        model = 'nero',
        price = 87515,
        category = 'super',
        type = 'automobile',
        hash = `nero`,
    },
    nero2 = {
        name = 'Nero Custom',
        brand = 'Truffade',
        model = 'nero2',
        price = 88062,
        category = 'super',
        type = 'automobile',
        hash = `nero2`,
    },
    nightblade = {
        name = 'Nightblade',
        brand = 'Western',
        model = 'nightblade',
        price = 24551,
        category = 'motorcycles',
        type = 'bike',
        hash = `nightblade`,
    },
    nightshade = {
        name = 'Nightshade',
        brand = 'Imponte',
        model = 'nightshade',
        price = 67005,
        category = 'muscle',
        type = 'automobile',
        hash = `nightshade`,
    },
    nightshark = {
        name = 'Nightshark',
        brand = 'HVY',
        model = 'nightshark',
        price = 61604,
        category = 'offroad',
        type = 'automobile',
        hash = `nightshark`,
    },
    nimbus = {
        name = 'Nimbus',
        brand = 'Buckingham',
        model = 'nimbus',
        price = 1896246,
        category = 'planes',
        type = 'plane',
        hash = `nimbus`,
    },
    ninef = {
        name = '9F',
        brand = 'Obey',
        model = 'ninef',
        price = 81139,
        category = 'sports',
        type = 'automobile',
        hash = `ninef`,
    },
    ninef2 = {
        name = '9F Cabrio',
        brand = 'Obey',
        model = 'ninef2',
        price = 81139,
        category = 'sports',
        type = 'automobile',
        hash = `ninef2`,
    },
    niobe = {
        name = 'Niobe',
        brand = 'Übermacht',
        model = 'niobe',
        price = 81139,
        category = 'sports',
        type = 'automobile',
        hash = `niobe`,
    },
    nokota = {
        name = 'P-45 Nokota',
        brand = '',
        model = 'nokota',
        price = 2079976,
        category = 'planes',
        type = 'plane',
        hash = `nokota`,
    },
    novak = {
        name = 'Novak',
        brand = 'Lampadati',
        model = 'novak',
        price = 78077,
        category = 'suvs',
        type = 'automobile',
        hash = `novak`,
    },
    omnis = {
        name = 'Omnis',
        brand = 'Obey',
        model = 'omnis',
        price = 73163,
        category = 'sports',
        type = 'automobile',
        hash = `omnis`,
    },
    omnisegt = {
        name = 'Omnis e-GT',
        brand = 'Obey',
        model = 'omnisegt',
        price = 80374,
        category = 'sports',
        type = 'automobile',
        hash = `omnisegt`,
    },
    openwheel1 = {
        name = 'BR8',
        brand = 'Benefactor',
        model = 'openwheel1',
        price = 106997,
        category = 'openwheel',
        type = 'automobile',
        hash = `openwheel1`,
    },
    openwheel2 = {
        name = 'DR1',
        brand = 'Declasse',
        model = 'openwheel2',
        price = 107315,
        category = 'openwheel',
        type = 'automobile',
        hash = `openwheel2`,
    },
    oppressor = {
        name = 'Oppressor',
        brand = 'Pegassi',
        model = 'oppressor',
        price = 76316,
        category = 'motorcycles',
        type = 'bike',
        hash = `oppressor`,
    },
    oppressor2 = {
        name = 'Oppressor Mk II',
        brand = 'Pegassi',
        model = 'oppressor2',
        price = 74148,
        category = 'motorcycles',
        type = 'bike',
        hash = `oppressor2`,
    },
    oracle = {
        name = 'Oracle XS',
        brand = 'Übermacht',
        model = 'oracle',
        price = 75192,
        category = 'coupes',
        type = 'automobile',
        hash = `oracle`,
    },
    oracle2 = {
        name = 'Oracle',
        brand = 'Übermacht',
        model = 'oracle2',
        price = 76907,
        category = 'coupes',
        type = 'automobile',
        hash = `oracle2`,
    },
    osiris = {
        name = 'Osiris',
        brand = 'Pegassi',
        model = 'osiris',
        price = 82889,
        category = 'super',
        type = 'automobile',
        hash = `osiris`,
    },
    outlaw = {
        name = 'Outlaw',
        brand = 'Nagasaki',
        model = 'outlaw',
        price = 60938,
        category = 'offroad',
        type = 'automobile',
        hash = `outlaw`,
    },
    packer = {
        name = 'Packer',
        brand = 'MTL',
        model = 'packer',
        price = 60714,
        category = 'commercial',
        type = 'automobile',
        hash = `packer`,
    },
    panthere = {
        name = 'Panthere',
        brand = 'Toundra',
        model = 'panthere',
        price = 82311,
        category = 'sports',
        type = 'automobile',
        hash = `panthere`,
    },
    panto = {
        name = 'Panto',
        brand = 'Benefactor',
        model = 'panto',
        price = 67549,
        category = 'compacts',
        type = 'automobile',
        hash = `panto`,
    },
    paradise = {
        name = 'Paradise',
        brand = 'Bravado',
        model = 'paradise',
        price = 63310,
        category = 'vans',
        type = 'automobile',
        hash = `paradise`,
    },
    paragon = {
        name = 'Paragon',
        brand = 'Enus',
        model = 'paragon',
        price = 75255,
        category = 'sports',
        type = 'automobile',
        hash = `paragon`,
    },
    paragon2 = {
        name = 'Paragon R (Armored)',
        brand = 'Enus',
        model = 'paragon2',
        price = 74900,
        category = 'sports',
        type = 'automobile',
        hash = `paragon2`,
    },
    paragon3 = {
        name = 'Paragon S',
        brand = 'Enus',
        model = 'paragon3',
        price = 74900,
        category = 'sports',
        type = 'automobile',
        hash = `paragon3`,
    },
    pariah = {
        name = 'Pariah',
        brand = 'Ocelot',
        model = 'pariah',
        price = 81207,
        category = 'sports',
        type = 'automobile',
        hash = `pariah`,
    },
    patriot = {
        name = 'Patriot',
        brand = 'Mammoth',
        model = 'patriot',
        price = 63246,
        category = 'suvs',
        type = 'automobile',
        hash = `patriot`,
    },
    patriot2 = {
        name = 'Patriot Stretch',
        brand = 'Mammoth',
        model = 'patriot2',
        price = 59238,
        category = 'suvs',
        type = 'automobile',
        hash = `patriot2`,
    },
    patriot3 = {
        name = 'Mil-Spec Patriot',
        brand = 'Mammoth',
        model = 'patriot3',
        price = 62402,
        category = 'offroad',
        type = 'automobile',
        hash = `patriot3`,
    },
    patrolboat = {
        name = 'Kurtz 31 Patrol Boat',
        brand = '',
        model = 'patrolboat',
        price = 430680,
        category = 'boats',
        type = 'boat',
        hash = `patrolboat`,
    },
    pbus = {
        name = 'Prison Bus',
        brand = 'Brute',
        model = 'pbus',
        price = 46633,
        category = 'emergency',
        type = 'automobile',
        hash = `pbus`,
    },
    pbus2 = {
        name = 'Festival Bus',
        brand = 'Brute',
        model = 'pbus2',
        price = 38412,
        category = 'service',
        type = 'automobile',
        hash = `pbus2`,
    },
    pcj = {
        name = 'PCJ-600',
        brand = 'Shitzu',
        model = 'pcj',
        price = 20997,
        category = 'motorcycles',
        type = 'bike',
        hash = `pcj`,
    },
    penetrator = {
        name = 'Penetrator',
        brand = 'Ocelot',
        model = 'penetrator',
        price = 81919,
        category = 'super',
        type = 'automobile',
        hash = `penetrator`,
    },
    penumbra = {
        name = 'Penumbra',
        brand = 'Maibatsu',
        model = 'penumbra',
        price = 67839,
        category = 'sports',
        type = 'automobile',
        hash = `penumbra`,
    },
    penumbra2 = {
        name = 'Penumbra FF',
        brand = 'Maibatsu',
        model = 'penumbra2',
        price = 74920,
        category = 'sports',
        type = 'automobile',
        hash = `penumbra2`,
    },
    peyote = {
        name = 'Peyote',
        brand = 'Vapid',
        model = 'peyote',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `peyote`,
    },
    peyote2 = {
        name = 'Peyote Gasser',
        brand = 'Vapid',
        model = 'peyote2',
        price = 77875,
        category = 'muscle',
        type = 'automobile',
        hash = `peyote2`,
    },
    peyote3 = {
        name = 'Peyote Custom',
        brand = 'Vapid',
        model = 'peyote3',
        price = 70150,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `peyote3`,
    },
    pfister811 = {
        name = '811',
        brand = 'Pfister',
        model = 'pfister811',
        price = 88499,
        category = 'super',
        type = 'automobile',
        hash = `pfister811`,
    },
    phantom = {
        name = 'Phantom',
        brand = 'JoBuilt',
        model = 'phantom',
        price = 56656,
        category = 'commercial',
        type = 'automobile',
        hash = `phantom`,
    },
    phantom2 = {
        name = 'Phantom Wedge',
        brand = 'JoBuilt',
        model = 'phantom2',
        price = 72230,
        category = 'commercial',
        type = 'automobile',
        hash = `phantom2`,
    },
    phantom3 = {
        name = 'Phantom Custom',
        brand = 'JoBuilt',
        model = 'phantom3',
        price = 69772,
        category = 'commercial',
        type = 'automobile',
        hash = `phantom3`,
    },
    phantom4 = {
        name = 'Phantom (Christmas)',
        brand = 'JoBuilt',
        model = 'phantom4',
        price = 56656,
        category = 'commercial',
        type = 'automobile',
        hash = `phantom4`,
    },
    phoenix = {
        name = 'Phoenix',
        brand = 'Imponte',
        model = 'phoenix',
        price = 78433,
        category = 'muscle',
        type = 'automobile',
        hash = `phoenix`,
    },
    picador = {
        name = 'Picador',
        brand = 'Cheval',
        model = 'picador',
        price = 67839,
        category = 'muscle',
        type = 'automobile',
        hash = `picador`,
    },
    pigalle = {
        name = 'Pigalle',
        brand = 'Lampadati',
        model = 'pigalle',
        price = 82282,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `pigalle`,
    },
    pipistrello = {
        name = 'Pipistrello',
        brand = 'Överflöd',
        model = 'pipistrello',
        price = 82282,
        category = 'super',
        type = 'automobile',
        hash = `pipistrello`,
    },
    pizzaboy = {
        name = 'Pizza Boy',
        brand = 'Pegassi',
        model = 'pizzaboy',
        price = 82282,
        category = 'motorcycles',
        type = 'bike',
        hash = `pizzaboy`,
    },
    polcaracara = {
        name = 'Caracara Pursuit',
        brand = 'Vapid',
        model = 'polcaracara',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `polcaracara`,
    },
    polcoquette4 = {
        name = 'Coquette D10 Pursuit',
        brand = 'Vapid',
        model = 'polcoquette4',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `polcoquette4`,
    },
    poldominator10 = {
        name = 'Dominator FX Interceptor',
        brand = 'Vapid',
        model = 'poldominator10',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `poldominator10`,
    },
    poldorado = {
        name = 'Dorado Cruiser',
        brand = 'Bravado',
        model = 'poldorado',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `poldorado`,
    },
    polfaction2 = {
        name = 'Outreach Faction',
        brand = 'Willard',
        model = 'poldorado',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `poldorado`,
    },
    polgauntlet = {
        name = 'Gauntlet Interceptor',
        brand = 'Bravado',
        model = 'polgauntlet',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `polgauntlet`,
    },
    polgreenwood = {
        name = 'Greenwood Cruiser',
        brand = 'Bravado',
        model = 'polgreenwood',
        price = 81768,
        category = 'emergency',
        type = 'automobile',
        hash = `polgreenwood`,
    },
    police = {
        name = 'Police Cruiser',
        brand = 'Vapid',
        model = 'police',
        price = 71053,
        category = 'emergency',
        type = 'automobile',
        hash = `police`,
    },
    police2 = {
        name = 'Police Cruiser',
        brand = 'Buffalo',
        model = 'police2',
        price = 76794,
        category = 'emergency',
        type = 'automobile',
        hash = `police2`,
    },
    police3 = {
        name = 'Police Cruiser (Interceptor)',
        brand = 'Vapid',
        model = 'police3',
        price = 80470,
        category = 'emergency',
        type = 'automobile',hash = `police3`,

    },
    police4 = {
        name = 'Unmarked Cruiser',
        brand = 'Vapid',
        model = 'police4',
        price = 71053,
        category = 'emergency',
        type = 'automobile',
        hash = `police4`,
    },
    police5 = {
        name = 'Stanier LE Cruiser',
        brand = 'Vapid',
        model = 'police5',
        price = 71053,
        category = 'emergency',
        type = 'automobile',
        hash = `police5`,
    },
    policeb = {
        name = 'Police Bike',
        brand = 'Western',
        model = 'policeb',
        price = 23078,
        category = 'emergency',
        type = 'automobile',
        hash = `policeb`,
    },
    policeold1 = {
        name = 'Police Rancher',
        brand = 'Declasse',
        model = 'policeold1',
        price = 58720,
        category = 'emergency',
        type = 'automobile',
        hash = `policeold1`,
    },
    policeold2 = {
        name = 'Police Roadcruiser',
        brand = 'Albany',
        model = 'policeold2',
        price = 71053,
        category = 'emergency',
        type = 'automobile',
        hash = `policeold2`,
    },
    policet = {
        name = 'Police Transporter',
        brand = 'Declasse',
        model = 'policet',
        price = 61195,
        category = 'emergency',
        type = 'automobile',
        hash = `policet`,
    },
    policet3 = {
        name = 'Burrito (Bail Enforcement)',
        brand = 'Declasse',
        model = 'policet3',
        price = 61195,
        category = 'emergency',
        type = 'automobile',
        hash = `policet3`,
    },
    polimpaler5 = {
        name = 'Impaler SZ Cruiser',
        brand = 'Declasse',
        model = 'polimpaler5',
        price = 61195,
        category = 'emergency',
        type = 'automobile',
        hash = `polimpaler5`,
    },
    polimpaler6 = {
        name = 'Impaler LX Cruiser',
        brand = 'Declasse',
        model = 'polimpaler6',
        price = 61195,
        category = 'emergency',
        type = 'automobile',
        hash = `polimpaler6`,
    },
    polmav = {
        name = 'Police Maverick',
        brand = 'Buckingham',
        model = 'polmav',
        price = 6293144,
        category = 'helicopters',
        type = 'heli',
        hash = `polmav`,
    },
    polterminus = {
        name = 'Terminus Patrol',
        brand = 'Canis',
        model = 'polterminus',
        price = 6293144,
        category = 'helicopters',
        type = 'heli',
        hash = `polterminus`,
    },
    pony = {
        name = 'Pony',
        brand = 'Brute',
        model = 'pony',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `pony`,
    },
    pony2 = {
        name = 'Pony (Smoke on the Water)',
        brand = 'Brute',
        model = 'pony2',
        price = 61195,
        category = 'vans',
        type = 'automobile',
        hash = `pony2`,
    },
    postlude = {
        name = 'Postlude',
        brand = 'Dinka',
        model = 'postlude',
        price = 69683,
        category = 'coupes',
        type = 'automobile',
        hash = `postlude`,
    },
    pounder = {
        name = 'Pounder',
        brand = 'MTL',
        model = 'pounder',
        price = 55550,
        category = 'commercial',
        type = 'automobile',
        hash = `pounder`,
    },
    pounder2 = {
        name = 'Pounder Custom',
        brand = 'MTL',
        model = 'pounder2',
        price = 60635,
        category = 'commercial',
        type = 'automobile',
        hash = `pounder2`,
    },
    powersurge = {
        name = 'Powersurge',
        brand = 'Western',
        model = 'powersurge',
        price = 23026,
        category = 'motorcycles',
        type = 'bike',
        hash = `powersurge`,
    },
    prairie = {
        name = 'Prairie',
        brand = 'Bollokan',
        model = 'prairie',
        price = 67519,
        category = 'compacts',
        type = 'automobile',
        hash = `prairie`,
    },
    pranger = {
        name = 'Park Ranger',
        brand = 'Declasse',
        model = 'pranger',
        price = 62617,
        category = 'emergency',
        type = 'automobile',
        hash = `pranger`,
    },
    predator = {
        name = 'Police Predator',
        brand = '',
        model = 'predator',
        price = 412680,
        category = 'boats',
        type = 'boat',
        hash = `predator`,
    },
    premier = {
        name = 'Premier',
        brand = 'Declasse',
        model = 'premier',
        price = 63694,
        category = 'sedans',
        type = 'automobile',
        hash = `premier`,
    },
    previon = {
        name = 'Previon',
        brand = 'Karin',
        model = 'previon',
        price = 78200,
        category = 'coupes',
        type = 'automobile',
        hash = `previon`,
    },
    primo = {
        name = 'Primo',
        brand = 'Albany',
        model = 'primo',
        price = 64174,
        category = 'sedans',
        type = 'automobile',
        hash = `primo`,
    },
    primo2 = {
        name = 'Primo Custom',
        brand = 'Albany',
        model = 'primo2',
        price = 64174,
        category = 'sedans',
        type = 'automobile',
        hash = `primo2`,
    },
    proptrailer = {
        name = 'Prop Trailer',
        brand = '',
        model = 'proptrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `proptrailer`,
    },
    prototipo = {
        name = 'X80 Proto',
        brand = 'Grotti',
        model = 'prototipo',
        price = 88458,
        category = 'super',
        type = 'automobile',
        hash = `prototipo`,
    },
    pyro = {
        name = 'Pyro',
        brand = 'Buckingham',
        model = 'pyro',
        price = 2140960,
        category = 'planes',
        type = 'plane',
        hash = `pyro`,
    },
    r300 = {
        name = '300R',
        brand = 'Annis',
        model = 'r300',
        price = 79467,
        category = 'sports',
        type = 'automobile',
        hash = `r300`,
    },
    radi = {
        name = 'Radius',
        brand = 'Vapid',
        model = 'radi',
        price = 65522,
        category = 'suvs',
        type = 'automobile',
        hash = `radi`,
    },
    raiden = {
        name = 'Raiden',
        brand = 'Coil',
        model = 'raiden',
        price = 64370,
        category = 'sports',
        type = 'automobile',
        hash = `raiden`,
    },
    raiju = {
        name = 'F-160 Raiju',
        brand = 'Mammoth',
        model = 'raiju',
        price = 2712193,
        category = 'planes',
        type = 'plane',
        hash = `raiju`,
    },
    raketrailer = {
        name = 'Trailer (Rake)',
        brand = '',
        model = 'raketrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `raketrailer`,
    },
    rallytruck = {
        name = 'Dune',
        brand = 'MTL',
        model = 'rallytruck',
        price = 67837,
        category = 'service',
        type = 'automobile',
        hash = `rallytruck`,
    },
    rancherxl = {
        name = 'Rancher XL',
        brand = 'Declasse',
        model = 'rancherxl',
        price = 58720,
        category = 'offroad',
        type = 'automobile',
        hash = `rancherxl`,
    },
    rancherxl2 = {
        name = 'Rancher XL (Snow)',
        brand = 'Declasse',
        model = 'rancherxl2',
        price = 58720,
        category = 'offroad',
        type = 'automobile',
        hash = `rancherxl2`,
    },
    rapidgt = {
        name = 'Rapid GT',
        brand = 'Dewbauchee',
        model = 'rapidgt',
        price = 83599,
        category = 'sports',
        type = 'automobile',
        hash = `rapidgt`,
    },
    rapidgt2 = {
        name = 'Rapid GT Cabrio',
        brand = 'Dewbauchee',
        model = 'rapidgt2',
        price = 83599,
        category = 'sports',
        type = 'automobile',
        hash = `rapidgt2`,
    },
    rapidgt3 = {
        name = 'Rapid GT Classic',
        brand = 'Dewbauchee',
        model = 'rapidgt3',
        price = 77766,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `rapidgt3`,
    },
    raptor = {
        name = 'Raptor',
        brand = 'BF',
        model = 'raptor',
        price = 72091,
        category = 'sports',
        type = 'automobile',
        hash = `raptor`,
    },
    ratbike = {
        name = 'Rat Bike',
        brand = 'Western',
        model = 'ratbike',
        price = 19441,
        category = 'motorcycles',
        type = 'bike',
        hash = `ratbike`,
    },
    ratel = {
        name = 'Ratel',
        brand = 'Vapid',
        model = 'ratel',
        price = 72415,
        category = 'offroad',
        type = 'automobile',
        hash = `ratel`,
    },
    ratloader = {
        name = 'Rat-Loader',
        brand = 'Bravado',
        model = 'ratloader',
        price = 61779,
        category = 'muscle',
        type = 'automobile',
        hash = `ratloader`,
    },
    ratloader2 = {
        name = 'Rat-Truck',
        brand = 'Bravado',
        model = 'ratloader2',
        price = 65562,
        category = 'muscle',
        type = 'automobile',
        hash = `ratloader2`,
    },
    rcbandito = {
        name = 'RC Bandito',
        brand = '',
        model = 'rcbandito',
        price = 44530,
        category = 'offroad',
        type = 'automobile',
        hash = `rcbandito`,
    },
    reaper = {
        name = 'Reaper',
        brand = 'Pegassi',
        model = 'reaper',
        price = 82990,
        category = 'super',
        type = 'automobile',
        hash = `reaper`,
    },
    rebel = {
        name = 'Rusty Rebel',
        brand = 'Karin',
        model = 'rebel',
        price = 63694,
        category = 'offroad',
        type = 'automobile',
        hash = `rebel`,
    },
    rebel2 = {
        name = 'Rebel',
        brand = 'Karin',
        model = 'rebel2',
        price = 63694,
        category = 'offroad',
        type = 'automobile',
        hash = `rebel2`,
    },
    rebla = {
        name = 'Rebla GTS',
        brand = 'Übermacht',
        model = 'rebla',
        price = 77543,
        category = 'suvs',
        type = 'automobile',
        hash = `rebla`,
    },
    reever = {
        name = 'Reever',
        brand = 'Western',
        model = 'reever',
        price = 26269,
        category = 'motorcycles',
        type = 'bike',
        hash = `reever`,
    },
    regina = {
        name = 'Regina',
        brand = 'Dundreary',
        model = 'regina',
        price = 50997,
        category = 'sedans',
        type = 'automobile',
        hash = `regina`,
    },
    remus = {
        name = 'Remus',
        brand = 'Annis',
        model = 'remus',
        price = 78314,
        category = 'sports',
        type = 'automobile',
        hash = `remus`,
    },
    rentalbus = {
        name = 'Rental Shuttle Bus',
        brand = 'Brute',
        model = 'rentalbus',
        price = 42321,
        category = 'service',
        type = 'automobile',
        hash = `rentalbus`,
    },
    retinue = {
        name = 'Retinue',
        brand = 'Vapid',
        model = 'retinue',
        price = 70738,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `retinue`,
    },
    retinue2 = {
        name = 'Retinue MKII',
        brand = 'Vapid',
        model = 'retinue2',
        price = 76099,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `retinue2`,
    },
    revolter = {
        name = 'Revolter',
        brand = 'Übermacht',
        model = 'revolter',
        price = 74872,
        category = 'sports',
        type = 'automobile',
        hash = `revolter`,
    },
    rhapsody = {
        name = 'Rhapsody',
        brand = 'Declasse',
        model = 'rhapsody',
        price = 68425,
        category = 'compacts',
        type = 'automobile',
        hash = `rhapsody`,
    },
    rhinehart = {
        name = 'Rhinehart',
        brand = 'Übermacht',
        model = 'rhinehart',
        price = 78276,
        category = 'sedans',
        type = 'automobile',
        hash = `rhinehart`,
    },
    rhino = {
        name = 'Rhino Tank',
        brand = '',
        model = 'rhino',
        price = 30613,
        category = 'military',
        type = 'automobile',
        hash = `rhino`,
    },
    riata = {
        name = 'Riata',
        brand = 'Vapid',
        model = 'riata',
        price = 66525,
        category = 'offroad',
        type = 'automobile',
        hash = `riata`,
    },
    riot = {
        name = 'Police Riot',
        brand = 'Brute',
        model = 'riot',
        price = 56588,
        category = 'emergency',
        type = 'automobile',
        hash = `riot`,
    },
    riot2 = {
        name = 'RCV',
        brand = 'Brute',
        model = 'riot2',
        price = 59094,
        category = 'emergency',
        type = 'automobile',
        hash = `riot2`,
    },
    ripley = {
        name = 'Ripley',
        brand = 'HVY',
        model = 'ripley',
        price = 38773,
        category = 'utility',
        type = 'automobile',
        hash = `ripley`,
    },
    rocoto = {
        name = 'Rocoto',
        brand = 'Obey',
        model = 'rocoto',
        price = 67755,
        category = 'suvs',
        type = 'automobile',
        hash = `rocoto`,
    },
    rogue = {
        name = 'Rogue',
        brand = 'Western',
        model = 'rogue',
        price = 2070400,
        category = 'planes',
        type = 'plane',
        hash = `rogue`,
    },
    romero = {
        name = 'Romero Hearse',
        brand = 'Chariot',
        model = 'romero',
        price = 53105,
        category = 'sedans',
        type = 'automobile',
        hash = `romero`,
    },
    rrocket = {
        name = 'Rampant Rocket',
        brand = 'Western',
        model = 'rrocket',
        price = 26257,
        category = 'motorcycles',
        type = 'bike',
        hash = `rrocket`,
    },
    rt3000 = {
        name = 'RT3000',
        brand = 'Dinka',
        model = 'rt3000',
        price = 80216,
        category = 'sports',
        type = 'automobile',
        hash = `rt3000`,
    },
    rubble = {
        name = 'Rubble',
        brand = 'JoBuilt',
        model = 'rubble',
        price = 54789,
        category = 'industrial',
        type = 'automobile',
        hash = `rubble`,
    },
    ruffian = {
        name = 'Ruffian',
        brand = 'Pegassi',
        model = 'ruffian',
        price = 24413,
        category = 'motorcycles',
        type = 'bike',
        hash = `ruffian`,
    },
    ruiner = {
        name = 'Ruiner',
        brand = 'Imponte',
        model = 'ruiner',
        price = 80117,
        category = 'muscle',
        type = 'automobile',
        hash = `ruiner`,
    },
    ruiner2 = {
        name = 'Ruiner 2000',
        brand = 'Imponte',
        model = 'ruiner2',
        price = 186491,
        category = 'muscle',
        type = 'automobile',
        hash = `ruiner2`,
    },
    ruiner3 = {
        name = 'Ruiner (Wrecked)',
        brand = 'Imponte',
        model = 'ruiner3',
        price = 80117,
        category = 'muscle',
        type = 'automobile',
        hash = `ruiner3`,
    },
    ruiner4 = {
        name = 'Ruiner ZZ-8',
        brand = 'Imponte',
        model = 'ruiner4',
        price = 80458,
        category = 'muscle',
        type = 'automobile',
        hash = `ruiner4`,
    },
    rumpo = {
        name = 'Rumpo',
        brand = 'Bravado',
        model = 'rumpo',
        price = 63310,
        category = 'vans',
        type = 'automobile',
        hash = `rumpo`,
    },
    rumpo2 = {
        name = 'Rumpo (Deludamol)',
        brand = 'Bravado',
        model = 'rumpo2',
        price = 63310,
        category = 'vans',
        type = 'automobile',
        hash = `rumpo2`,
    },
    rumpo3 = {
        name = 'Rumpo Custom',
        brand = 'Bravado',
        model = 'rumpo3',
        price = 59206,
        category = 'vans',
        type = 'automobile',
        hash = `rumpo3`,
    },
    ruston = {
        name = 'Ruston',
        brand = 'Hijak',
        model = 'ruston',
        price = 78365,
        category = 'sports',
        type = 'automobile',
        hash = `ruston`,
    },
    s80 = {
        name = 'S80RR',
        brand = 'Annis',
        model = 's80',
        price = 83208,
        category = 'super',
        type = 'automobile',
        hash = `s80`,
    },
    sabregt = {
        name = 'Sabre Turbo',
        brand = 'Declasse',
        model = 'sabregt',
        price = 77450,
        category = 'muscle',
        type = 'automobile',
        hash = `sabregt`,
    },
    sabregt2 = {
        name = 'Sabre GT',
        brand = 'Declasse',
        model = 'sabregt2',
        price = 77489,
        category = 'muscle',
        type = 'automobile',
        hash = `sabregt2`,
    },
    sadler = {
        name = 'Sadler',
        brand = 'Vapid',
        model = 'sadler',
        price = 62297,
        category = 'utility',
        type = 'automobile',
        hash = `sadler`,
    },
    sadler2 = {
        name = 'Sadler (Snow)',
        brand = 'Vapid',
        model = 'sadler2',
        price = 62297,
        category = 'utility',
        type = 'automobile',
        hash = `sadler2`,
    },
    sanchez = {
        name = 'Sanchez (livery)',
        brand = 'Maibatsu',
        model = 'sanchez',
        price = 20686,
        category = 'motorcycles',
        type = 'bike',
        hash = `sanchez`,
    },
    sanchez2 = {
        name = 'Sanchez',
        brand = 'Maibatsu',
        model = 'sanchez2',
        price = 20686,
        category = 'motorcycles',
        type = 'bike',
        hash = `sanchez2`,
    },
    sanctus = {
        name = 'Sanctus',
        brand = 'LCC',
        model = 'sanctus',
        price = 24351,
        category = 'motorcycles',
        type = 'bike',
        hash = `sanctus`,
    },
    sandking = {
        name = 'Sandking',
        brand = 'Vapid',
        model = 'sandking',
        price = 63694,
        category = 'offroad',
        type = 'automobile',
        hash = `sandking`,
    },
    sandking2 = {
        name = 'Sandking SWB',
        brand = 'Vapid',
        model = 'sandking2',
        price = 63694,
        category = 'offroad',
        type = 'automobile',
        hash = `sandking2`,
    },
    savage = {
        name = 'Savage',
        brand = '',
        model = 'savage',
        price = 6962237,
        category = 'helicopters',
        type = 'heli',
        hash = `savage`,
    },
    savestra = {
        name = 'Savestra',
        brand = 'Annis',
        model = 'savestra',
        price = 72617,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `savestra`,
    },
    sc1 = {
        name = 'SC1',
        brand = 'Übermacht',
        model = 'sc1',
        price = 79257,
        category = 'super',
        type = 'automobile',
        hash = `sc1`,
    },
    scarab = {
        name = 'Apocalypse Scarab',
        brand = 'HVY',
        model = 'scarab',
        price = 39724,
        category = 'military',
        type = 'automobile',
        hash = `scarab`,
    },
    scarab2 = {
        name = 'Future Shock Scarab',
        brand = 'HVY',
        model = 'scarab2',
        price = 39724,
        category = 'military',
        type = 'automobile',
        hash = `scarab2`,
    },
    scarab3 = {
        name = 'Nightmare Scarab',
        brand = 'HVY',
        model = 'scarab3',
        price = 39724,
        category = 'military',
        type = 'automobile',
        hash = `scarab3`,
    },
    schafter2 = {
        name = 'Schafter',
        brand = 'Benefactor',
        model = 'schafter2',
        price = 65682,
        category = 'sedans',
        type = 'automobile',
        hash = `schafter2`,
    },
    schafter3 = {
        name = 'Schafter V12',
        brand = 'Benefactor',
        model = 'schafter3',
        price = 83088,
        category = 'sports',
        type = 'automobile',
        hash = `schafter3`,
    },
    schafter4 = {
        name = 'Schafter LWB',
        brand = 'Benefactor',
        model = 'schafter4',
        price = 65602,
        category = 'sports',
        type = 'automobile',
        hash = `schafter4`,
    },
    schafter5 = {
        name = 'Schafter V12 (Armored)',
        brand = 'Benefactor',
        model = 'schafter5',
        price = 83008,
        category = 'sedans',
        type = 'automobile',
        hash = `schafter5`,
    },
    schafter6 = {
        name = 'Schafter LWB (Armored)',
        brand = 'Benefactor',
        model = 'schafter6',
        price = 62476,
        category = 'sedans',
        type = 'automobile',
        hash = `schafter6`,
    },
    schlagen = {
        name = 'Schlagen GT',
        brand = 'Benefactor',
        model = 'schlagen',
        price = 86368,
        category = 'sports',
        type = 'automobile',
        hash = `schlagen`,
    },
    schwarzer = {
        name = 'Schwartzer',
        brand = 'Benefactor',
        model = 'schwarzer',
        price = 80250,
        category = 'sports',
        type = 'automobile',
        hash = `schwarzer`,
    },
    scorcher = {
        name = 'Scorcher',
        brand = 'PED',
        model = 'scorcher',
        price = 2782,
        category = 'cycles',
        type = 'bike',
        hash = `scorcher`,
    },
    scramjet = {
        name = 'Scramjet',
        brand = 'Declasse',
        model = 'scramjet',
        price = 370681,
        category = 'super',
        type = 'automobile',
        hash = `scramjet`,
    },
    scrap = {
        name = 'Scrap Truck',
        brand = 'Vapid',
        model = 'scrap',
        price = 52886,
        category = 'utility',
        type = 'automobile',
        hash = `scrap`,
    },
    seabreeze = {
        name = 'Seabreeze',
        brand = 'Western',
        model = 'seabreeze',
        price = 2211729,
        category = 'planes',
        type = 'plane',
        hash = `seabreeze`,
    },
    seashark = {
        name = 'Seashark',
        brand = 'Speedophile',
        model = 'seashark',
        price = 414680,
        category = 'boats',
        type = 'boat',
        hash = `seashark`,
    },
    seashark2 = {
        name = 'Seashark (Lifeguard)',
        brand = 'Speedophile',
        model = 'seashark2',
        price = 414680,
        category = 'boats',
        type = 'boat',
        hash = `seashark2`,
    },
    seashark3 = {
        name = 'Seashark (Yacht)',
        brand = 'Speedophile',
        model = 'seashark3',
        price = 414680,
        category = 'boats',
        type = 'boat',
        hash = `seashark3`,
    },
    seasparrow = {
        name = 'Sea Sparrow',
        brand = '',
        model = 'seasparrow',
        price = 6293144,
        category = 'helicopters',
        type = 'heli',
        hash = `seasparrow`,
    },
    seasparrow2 = {
        name = 'Sparrow',
        brand = '',
        model = 'seasparrow2',
        price = 7760028,
        category = 'helicopters',
        type = 'heli',
        hash = `seasparrow2`,
    },
    seasparrow3 = {
        name = 'Sparrow (Prop)',
        model = 'seasparrow3',
        price = 7760028,
        brand = '',
        category = 'helicopters',
        type = 'heli',
        hash = `seasparrow3`,
    },
    seminole = {
        name = 'Seminole',
        brand = 'Canis',
        model = 'seminole',
        price = 61393,
        category = 'suvs',
        type = 'automobile',
        hash = `seminole`,
    },
    seminole2 = {
        name = 'Seminole Frontier',
        brand = 'Canis',
        model = 'seminole2',
        price = 64904,
        category = 'suvs',
        type = 'automobile',
        hash = `seminole2`,
    },
    sentinel = {
        name = 'Sentinel',
        brand = 'Übermacht',
        model = 'sentinel',
        price = 75585,
        category = 'coupes',
        type = 'automobile',
        hash = `sentinel`,
    },
    sentinel2 = {
        name = ' Sentinel XS',
        brand = 'Übermacht',
        model = 'sentinel2',
        price = 75585,
        category = 'coupes',
        type = 'automobile',
        hash = `sentinel2`,
    },
    sentinel3 = {
        name = 'Sentinel Classic',
        brand = 'Übermacht',
        model = 'sentinel3',
        price = 74166,
        category = 'sports',
        type = 'automobile',
        hash = `sentinel3`,
    },
    sentinel4 = {
        name = 'Sentinel Classic Widebody',
        brand = 'Übermacht',
        model = 'sentinel4',
        price = 78990,
        category = 'sports',
        type = 'automobile',
        hash = `sentinel4`,
    },
    serrano = {
        name = 'Serrano',
        brand = 'Benefactor',
        model = 'serrano',
        price = 66517,
        category = 'suvs',
        type = 'automobile',
        hash = `serrano`,
    },
    seven70 = {
        name = 'Seven-70',
        brand = 'Dewbauchee',
        model = 'seven70',
        price = 84734,
        category = 'sports',
        type = 'automobile',
        hash = `seven70`,
    },
    shamal = {
        name = 'Shamal',
        brand = 'Buckingham',
        model = 'shamal',
        price = 1818934,
        category = 'planes',
        type = 'plane',
        hash = `shamal`,
    },
    sheava = {
        name = 'ETR1',
        brand = 'Emperor',
        model = 'sheava',
        price = 81139,
        category = 'super',
        type = 'automobile',
        hash = `sheava`,
    },
    sheriff = {
        name = 'Sheriff Cruiser',
        brand = 'Vapid',
        model = 'sheriff',
        price = 71186,
        category = 'emergency',
        type = 'automobile',
        hash = `sheriff`,
    },
    sheriff2 = {
        name = 'Sheriff SUV',
        brand = 'Declasse',
        model = 'sheriff2',
        price = 62617,
        category = 'emergency',
        type = 'automobile',
        hash = `sheriff2`,
    },
    shinobi = {
        name = 'Shinobi',
        brand = 'Nagasaki',
        model = 'shinobi',
        price = 27244,
        category = 'motorcycles',
        type = 'bike',
        hash = `shinobi`,
    },
    shotaro = {
        name = 'Shotaro Concept',
        brand = 'Nagasaki',
        model = 'shotaro',
        price = 26899,
        category = 'motorcycles',
        type = 'bike',
        hash = `shotaro`,
    },
    skylift = {
        name = 'Skylift',
        brand = 'HVY',
        model = 'skylift',
        price = 5363475,
        category = 'helicopters',
        type = 'heli',
        hash = `skylift`,
    },
    slamtruck = {
        name = 'Slam Truck',
        brand = 'Vapid',
        model = 'slamtruck',
        price = 62937,
        category = 'utility',
        type = 'automobile',
        hash = `slamtruck`,
    },
    slamvan = {
        name = 'Slam Van',
        brand = 'Vapid',
        model = 'slamvan',
        price = 66207,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan`,
    },
    slamvan2 = {
        name = 'Lost Slam Van',
        brand = 'Vapid',
        model = 'slamvan2',
        price = 67165,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan2`,
    },
    slamvan3 = {
        name = 'Slam Van Custom',
        brand = 'Vapid',
        model = 'slamvan3',
        price = 67005,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan3`,
    },
    slamvan4 = {
        name = 'Apocalypse Slamvan',
        brand = 'Vapid',
        model = 'slamvan4',
        price = 68476,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan4`,
    },
    slamvan5 = {
        name = 'Future Shock Slamvan',
        brand = 'Vapid',
        model = 'slamvan5',
        price = 68476,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan5`,
    },
    slamvan6 = {
        name = 'Nightmare Slamvan',
        brand = 'Vapid',
        model = 'slamvan6',
        price = 68476,
        category = 'muscle',
        type = 'automobile',
        hash = `slamvan6`,
    },
    sm722 = {
        name = 'SM722',
        brand = 'Benefactor',
        model = 'sm722',
        price = 80260,
        category = 'sports',
        type = 'automobile',
        hash = `sm722`,
    },
    sovereign = {
        name = 'Sovereign',
        brand = 'Western',
        model = 'sovereign',
        price = 23078,
        category = 'motorcycles',
        type = 'bike',
        hash = `sovereign`,
    },
    specter = {
        name = 'Specter',
        brand = 'Dewbauchee',
        model = 'specter',
        price = 81060,
        category = 'sports',
        type = 'automobile',
        hash = `specter`,
    },
    specter2 = {
        name = 'Specter Custom',
        brand = 'Dewbauchee',
        model = 'specter2',
        price = 82332,
        category = 'sports',
        type = 'automobile',
        hash = `specter2`,
    },
    speeder = {
        name = 'Speeder (Yacht)',
        brand = 'Pegassi',
        model = 'speeder',
        price = 462680,
        category = 'boats',
        type = 'boat',
        hash = `speeder`,
    },
    speeder2 = {
        name = 'Speeder',
        brand = 'Pegassi',
        model = 'speeder2',
        price = 462680,
        category = 'boats',
        type = 'boat',
        hash = `speeder2`,
    },
    speedo = {
        name = 'Speedo',
        brand = 'Vapid',
        model = 'speedo',
        price = 66002,
        category = 'vans',
        type = 'automobile',
        hash = `speedo`,
    },
    speedo2 = {
        name = 'Clown Van',
        brand = 'Vapid',
        model = 'speedo2',
        price = 66002,
        category = 'vans',
        type = 'automobile',
        hash = `speedo2`,
    },
    speedo4 = {
        name = 'Speedo Custom (Nightclub)',
        brand = 'Vapid',
        model = 'speedo4',
        price = 70653,
        category = 'vans',
        type = 'automobile',
        hash = `speedo4`,
    },
    speedo5 = {
        name = 'Speedo Custom',
        brand = 'Vapid',
        model = 'speedo5',
        price = 70653,
        category = 'vans',
        type = 'automobile',
        hash = `speedo5`,
    },
    squaddie = {
        name = 'Squaddie',
        brand = 'Mammoth',
        model = 'squaddie',
        price = 63238,
        category = 'suvs',
        type = 'automobile',
        hash = `squaddie`,
    },
    squalo = {
        name = 'Squalo',
        brand = 'Shitzu',
        model = 'squalo',
        price = 362680,
        category = 'boats',
        type = 'boat',
        hash = `squalo`,
    },
    stafford = {
        name = 'Stafford',
        brand = 'Enus',
        model = 'stafford',
        price = 62882,
        category = 'sedans',
        type = 'automobile',
        hash = `stafford`,
    },
    stalion = {
        name = 'Stallion',
        brand = 'Declasse',
        model = 'stalion',
        price = 74781,
        category = 'muscle',
        type = 'automobile',
        hash = `stalion`,
    },
    stalion2 = {
        name = 'Stallion Burgershot',
        brand = 'Declasse',
        model = 'stalion2',
        price = 79092,
        category = 'muscle',
        type = 'automobile',
        hash = `stalion2`,
    },
    stanier = {
        name = 'Stanier',
        brand = 'Vapid',
        model = 'stanier',
        price = 71053,
        category = 'sedans',
        type = 'automobile',
        hash = `stanier`,
    },
    starling = {
        name = 'LF-22 Starling',
        brand = '',
        model = 'starling',
        price = 5890500,
        category = 'planes',
        type = 'plane',
        hash = `starling`,
    },
    stinger = {
        name = 'Stinger',
        brand = 'Grotti',
        model = 'stinger',
        price = 74712,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `stinger`,
    },
    stingergt = {
        name = 'Stinger GT',
        brand = 'Grotti',
        model = 'stingergt',
        price = 74712,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `stingergt`,
    },
    stingertt = {
        name = 'Itali GTO Stinger TT',
        brand = 'Grotti',
        model = 'stingertt',
        price = 87213,
        category = 'sports',
        type = 'automobile',
        hash = `stingertt`,
    },
    stockade = {
        name = 'Stockade',
        brand = 'Brute',
        model = 'stockade',
        price = 56588,
        category = 'commercial',
        type = 'automobile',
        hash = `stockade`,
    },
    stockade3 = {
        name = 'Stockade (Bobcat Security/Snow)',
        brand = 'Brute',
        model = 'stockade3',
        price = 56588,
        category = 'commercial',
        type = 'automobile',
        hash = `stockade3`,
    },
    stratum = {
        name = 'Stratum',
        brand = 'Zirconium',
        model = 'stratum',
        price = 68894,
        category = 'sedans',
        type = 'automobile',
        hash = `stratum`,
    },
    streamer216 = {
        name = 'Streamer216',
        brand = 'Mammoth',
        model = 'streamer216',
        price = 1574232,
        category = 'planes',
        type = 'plane',
        hash = `streamer216`,
    },
    streiter = {
        name = 'Streiter',
        brand = 'Benefactor',
        model = 'streiter',
        price = 71204,
        category = 'sports',
        type = 'automobile',
        hash = `streiter`,
    },
    stretch = {
        name = 'Stretch',
        brand = 'Dundreary',
        model = 'stretch',
        price = 63950,
        category = 'sedans',
        type = 'automobile',
        hash = `stretch`,
    },
    strikeforce = {
        name = 'B-11 Strikeforce',
        brand = '',
        model = 'strikeforce',
        price = 1810529,
        category = 'planes',
        type = 'plane',
        hash = `strikeforce`,
    },
    stromberg = {
        name = 'Stromberg',
        brand = 'Ocelot',
        model = 'stromberg',
        price = 76526,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `stromberg`,
    },
    stryder = {
        name = 'Stryder',
        brand = 'Nagasaki',
        model = 'stryder',
        price = 26711,
        category = 'motorcycles',
        type = 'bike',
        hash = `stryder`,
    },
    stunt = {
        name = 'Mallard',
        brand = 'Western Company',
        model = 'stunt',
        price = 1702472,
        category = 'planes',
        type = 'plane',
        hash = `stunt`,
    },
    submersible = {
        name = 'Submersible',
        brand = '',
        model = 'submersible',
        price = 718616,
        category = 'boats',
        type = 'submarine',
        hash = `submersible`,
    },
    submersible2 = {
        name = 'Kraken',
        brand = 'Kraken',
        model = 'submersible2',
        price = 787416,
        category = 'boats',
        type = 'submarine',
        hash = `submersible2`,
    },
    sugoi = {
        name = 'Sugoi',
        brand = 'Dinka',
        model = 'sugoi',
        price = 75269,
        category = 'sports',
        type = 'automobile',
        hash = `sugoi`,
    },
    sultan = {
        name = 'Sultan',
        brand = 'Karin',
        model = 'sultan',
        price = 74392,
        category = 'sports',
        type = 'automobile',
        hash = `sultan`,
    },
    sultan2 = {
        name = 'Sultan Custom',
        brand = 'Karin',
        model = 'sultan2',
        price = 77184,
        category = 'sports',
        type = 'automobile',
        hash = `sultan2`,
    },
    sultan3 = {
        name = 'Sultan Classic Custom',
        brand = 'Karin',
        model = 'sultan3',
        price = 78067,
        category = 'sports',
        type = 'automobile',
        hash = `sultan3`,
    },
    sultanrs = {
        name = 'Sultan RS',
        brand = 'Karin',
        model = 'sultanrs',
        price = 82197,
        category = 'super',
        type = 'automobile',
        hash = `sultanrs`,
    },
    suntrap = {
        name = 'Suntrap',
        brand = 'Shitzu',
        model = 'suntrap',
        price = 362680,
        category = 'boats',
        type = 'boat',
        hash = `suntrap`,
    },
    superd = {
        name = 'Super Diamond',
        brand = 'Enus',
        model = 'superd',
        price = 74712,
        category = 'sedans',
        type = 'automobile',
        hash = `superd`,
    },
    supervolito = {
        name = 'SuperVolito',
        brand = 'Buckingham',
        model = 'supervolito',
        price = 6905749,
        category = 'helicopters',
        type = 'heli',
        hash = `supervolito`,
    },
    supervolito2 = {
        name = 'SuperVolito Carbon',
        brand = 'Buckingham',
        model = 'supervolito2',
        price = 6905749,
        category = 'helicopters',
        type = 'heli',
        hash = `supervolito2`,
    },
    surano = {
        name = 'Surano',
        brand = 'Benefactor',
        model = 'surano',
        price = 83971,
        category = 'sports',
        type = 'automobile',
        hash = `surano`,
    },
    surfer = {
        name = 'Surfer',
        brand = 'BF',
        model = 'surfer',
        price = 34505,
        category = 'vans',
        type = 'automobile',
        hash = `surfer`,
    },
    surfer2 = {
        name = 'Surfer',
        brand = 'BF',
        model = 'surfer2',
        price = 34505,
        category = 'vans',
        type = 'automobile',
        hash = `surfer2`,
    },
    surfer3 = {
        name = 'Surfer Custom',
        brand = 'BF',
        model = 'surfer3',
        price = 34505,
        category = 'vans',
        type = 'automobile',
        hash = `surfer3`,
    },
    surge = {
        name = 'Surge',
        brand = 'Cheval',
        model = 'surge',
        price = 40605,
        category = 'sedans',
        type = 'automobile',
        hash = `surge`,
    },
    swift = {
        name = 'Swift',
        brand = 'Buckingham',
        model = 'swift',
        price = 6504898,
        category = 'helicopters',
        type = 'heli',
        hash = `swift`,
    },
    swift2 = {
        name = 'Swift Deluxe',
        brand = 'Buckingham',
        model = 'swift2',
        price = 6588870,
        category = 'helicopters',
        type = 'heli',
        hash = `swift2`,
    },
    swinger = {
        name = 'Swinger',
        brand = 'Ocelot',
        model = 'swinger',
        price = 81274,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `swinger`,
    },
    t20 = {
        name = 'Progen T20',
        brand = 'Progen',
        model = 't20',
        price = 83022,
        category = 'super',
        type = 'automobile',
        hash = `t20`,
    },
    taco = {
        name = 'Taco Van',
        brand = 'Brute',
        model = 'taco',
        price = 47273,
        category = 'vans',
        type = 'automobile',
        hash = `taco`,
    },
    tahoma = {
        name = 'Tahoma Coupe',
        brand = 'Declasse',
        model = 'tahoma',
        price = 72249,
        category = 'muscle',
        type = 'automobile',
        hash = `tahoma`,
    },
    tailgater = {
        name = 'Tailgater',
        brand = 'Obey',
        model = 'tailgater',
        price = 64174,
        category = 'sedans',
        type = 'automobile',
        hash = `tailgater`,
    },
    tailgater2 = {
        name = 'Tailgater S',
        brand = 'Obey',
        model = 'tailgater2',
        price = 77411,
        category = 'sedans',
        type = 'automobile',
        hash = `tailgater2`,
    },
    taipan = {
        name = 'Taipan',
        brand = 'Cheval',
        model = 'taipan',
        price = 89537,
        category = 'super',
        type = 'automobile',
        hash = `taipan`,
    },
    tampa = {
        name = 'Tampa',
        brand = 'Declasse',
        model = 'tampa',
        price = 70442,
        category = 'muscle',
        type = 'automobile',
        hash = `tampa`,
    },
    tampa2 = {
        name = 'Drift Tampa',
        brand = 'Declasse',
        model = 'tampa2',
        price = 75751,
        category = 'sports',
        type = 'automobile',
        hash = `tampa2`,
    },
    tampa3 = {
        name = 'Weaponized Tampa',
        brand = 'Declasse',
        model = 'tampa3',
        price = 75751,
        category = 'muscle',
        type = 'automobile',
        hash = `tampa3`,
    },
    tanker = {
        name = 'Tanker Trailer',
        brand = '',
        model = 'tanker',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `tanker`,
    },
    tanker2 = {
        name = 'Tanker Trailer',
        brand = '',
        model = 'tanker2',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `tanker2`,
    },
    tankercar = {
        name = 'Freight Train (Tanker Trailer)',
        brand = '',
        model = 'tankercar',
        price = 194680,
        category = 'trains',
        type = 'train',
        hash = `tankercar`,
    },
    taxi = {
        name = 'Taxi',
        brand = 'Vapid',
        model = 'taxi',
        price = 71053,
        category = 'service',
        type = 'automobile',
        hash = `taxi`,
    },
    technical = {
        name = 'Technical',
        brand = 'Karin',
        model = 'technical',
        price = 63854,
        category = 'offroad',
        type = 'automobile',
        hash = `technical`,
    },
    technical2 = {
        name = 'Technical Aqua',
        brand = 'Karin',
        model = 'technical2',
        price = 65910,
        category = 'offroad',
        type = 'automobile',
        hash = `technical2`,
    },
    technical3 = {
        name = 'Technical Custom',
        brand = 'Karin',
        model = 'technical3',
        price = 63854,
        category = 'offroad',
        type = 'automobile',
        hash = `technical3`,
    },
    tempesta = {
        name = 'Tempesta',
        brand = 'Pegassi',
        model = 'tempesta',
        price = 81980,
        category = 'super',
        type = 'automobile',
        hash = `tempesta`,
    },
    tenf = {
        name = '10F',
        brand = 'Obey',
        model = 'tenf',
        price = 82128,
        category = 'sports',
        type = 'automobile',
        hash = `tenf`,
    },
    tenf2 = {
        name = '10F Widebody',
        brand = 'Obey',
        model = 'tenf2',
        price = 82729,
        category = 'sports',
        type = 'automobile',
        hash = `tenf2`,
    },
    terbyte = {
        name = 'Terrorbyte',
        brand = 'Benefactor',
        model = 'terbyte',
        price = 50669,
        category = 'commercial',
        type = 'automobile',
        hash = `terbyte`,
    },
    terminus = {
        name = 'Terminus',
        brand = 'Canis',
        model = 'terminus',
        price = 71490,
        category = 'offroad',
        type = 'automobile',
        hash = `terminus`,
    },
    tezeract = {
        name = 'Tezeract',
        brand = 'Pegassi',
        model = 'tezeract',
        price = 76330,
        category = 'super',
        type = 'automobile',
        hash = `tezeract`,
    },
    thrax = {
        name = 'Thrax',
        brand = 'Truffade',
        model = 'thrax',
        price = 83929,
        category = 'super',
        type = 'automobile',
        hash = `thrax`,
    },
    thrust = {
        name = 'Thrust',
        brand = 'Dinka',
        model = 'thrust',
        price = 25865,
        category = 'motorcycles',
        type = 'bike',
        hash = `thrust`,
    },
    thruster = {
        name = 'Thruster',
        brand = 'Mammoth',
        model = 'thruster',
        price = 6768641,
        category = 'military',
        type = 'automobile',
        hash = `thruster`,
    },
    tigon = {
        name = 'Tigon',
        brand = 'Lampadati',
        model = 'tigon',
        price = 86363,
        category = 'super',
        type = 'automobile',
        hash = `tigon`,
    },
    tiptruck = {
        name = 'Tipper',
        brand = 'Brute',
        model = 'tiptruck',
        price = 47273,
        category = 'industrial',
        type = 'automobile',
        hash = `tiptruck`,
    },
    tiptruck2 = {
        name = 'Tipper',
        brand = 'Brute',
        model = 'tiptruck2',
        price = 47273,
        category = 'industrial',
        type = 'automobile',
        hash = `tiptruck2`,
    },
    titan = {
        name = 'Titan',
        brand = '',
        model = 'titan',
        price = 1521752,
        category = 'planes',
        type = 'plane',
        hash = `titan`,
    },
    titan2 = {
        name = 'Titan 250 D',
        brand = 'Eberhard',
        model = 'titan2',
        price = 1521752,
        category = 'planes',
        type = 'plane',
        hash = `titan2`,
    },
    toreador = {
        name = 'Toreador',
        brand = 'Pegassi',
        model = 'toreador',
        price = 235089,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `toreador`,
    },
    torero = {
        name = 'Torero',
        brand = 'Pegassi',
        model = 'torero',
        price = 78457,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `torero`,
    },
    torero2 = {
        name = 'Torero XO',
        brand = 'Pegassi',
        model = 'torero2',
        price = 87548,
        category = 'super',
        type = 'automobile',
        hash = `torero2`,
    },
    tornado = {
        name = 'Tornado',
        brand = 'Declasse',
        model = 'tornado',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado`,
    },
    tornado2 = {
        name = 'Tornado Gang',
        brand = 'Declasse',
        model = 'tornado2',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado2`,
    },
    tornado3 = {
        name = 'Tornado (Beater)',
        brand = 'Declasse',
        model = 'tornado3',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado3`,
    },
    tornado4 = {
        name = 'Tornado (Mariachi)',
        brand = 'Declasse',
        model = 'tornado4',
        price = 60635,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado4`,
    },
    tornado5 = {
        name = 'Tornado Custom',
        brand = 'Declasse',
        model = 'tornado5',
        price = 60890,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado5`,
    },
    tornado6 = {
        name = 'Tornado Rat Rod',
        brand = 'Declasse',
        model = 'tornado6',
        price = 66096,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `tornado6`,
    },
    toro = {
        name = 'Toro',
        brand = 'Lampadati',
        model = 'toro',
        price = 486680,
        category = 'boats',
        type = 'boat',
        hash = `toro`,
    },
    toro2 = {
        name = 'Toro (Yacht)',
        brand = 'Lampadati',
        model = 'toro2',
        price = 486680,
        category = 'boats',
        type = 'boat',
        hash = `toro2`,
    },
    toros = {
        name = 'Toros',
        brand = 'Pegassi',
        model = 'toros',
        price = 81091,
        category = 'suvs',
        type = 'automobile',
        hash = `toros`,
    },
    tourbus = {
        name = 'Tour Bus',
        brand = 'Brute',
        model = 'tourbus',
        price = 42321,
        category = 'service',
        type = 'automobile',
        hash = `tourbus`,
    },
    towtruck = {
        name = 'Tow Truck',
        brand = 'Vapid',
        model = 'towtruck',
        price = 57488,
        category = 'utility',
        type = 'automobile',
        hash = `towtruck`,
    },
    towtruck2 = {
        name = 'Tow Truck (Small)',
        brand = 'Vapid',
        model = 'towtruck2',
        price = 54821,
        category = 'utility',
        type = 'automobile',
        hash = `towtruck2`,
    },
    towtruck3 = {
        name = 'Tow Truck (Beater)',
        brand = 'Vapid',
        model = 'towtruck3',
        price = 62901,
        category = 'utility',
        type = 'automobile',
        hash = `towtruck3`,
    },
    towtruck4 = {
        name = 'Tow Truck',
        brand = 'Vapid',
        model = 'towtruck4',
        price = 62901,
        category = 'utility',
        type = 'automobile',
        hash = `towtruck4`,
    },
    tr2 = {
        name = 'Trailer (Car Carrier)',
        brand = '',
        model = 'tr2',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `tr2`,
    },
    tr3 = {
        name = 'Trailer (Boat)',
        brand = '',
        model = 'tr3',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `tr3`,
    },
    tr4 = {
        name = 'Trailer (Packed Car Carrier)',
        brand = '',
        model = 'tr4',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `tr4`,
    },
    tractor = {
        name = 'Tractor',
        brand = '',
        model = 'tractor',
        price = 22677,
        category = 'utility',
        type = 'automobile',
        hash = `tractor`,
    },
    tractor2 = {
        name = 'Fieldmaster',
        brand = 'Stanley',
        model = 'tractor2',
        price = 24703,
        category = 'utility',
        type = 'automobile',
        hash = `tractor2`,
    },
    tractor3 = {
        name = 'Fieldmaster (Snow)',
        brand = 'Stanley',
        model = 'tractor3',
        price = 24703,
        category = 'utility',
        type = 'automobile',
        hash = `tractor3`,
    },
    trailerlarge = {
        name = 'Mobile Operations Center',
        brand = 'Pegasus',
        model = 'trailerlarge',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailerlarge`,
    },
    trailerlogs = {
        name = 'Trailer (Logs)',
        brand = '',
        model = 'trailerlogs',
        price = 5748,
        category = 'utility',
        type = 'trailer',
        hash = `trailerlogs`,
    },
    trailers = {
        name = 'Trailer (Container)',
        brand = '',
        model = 'trailers',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailers`,
    },
    trailers2 = {
        name = 'Trailer (Box)',
        brand = '',
        model = 'trailers2',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailers2`,
    },
    trailers3 = {
        name = 'Trailer (Ramp box)',
        brand = '',
        model = 'trailers3',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailers3`,
    },
    trailers4 = {
        name = 'Trailer (Container)',
        brand = '',
        model = 'trailers4',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailers4`,
    },
    trailers5 = {
        name = 'Trailer (Christmas)',
        brand = '',
        model = 'trailers5',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailers5`,
    },
    trailersmall = {
        name = 'Trailer (Storage/generator)',
        brand = '',
        model = 'trailersmall',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trailersmall`,
    },
    trailersmall2 = {
        name = 'Anti-Aircraft Trailer',
        brand = 'Vom Feuer',
        model = 'trailersmall2',
        price = 5668,
        category = 'military',
        type = 'trailer',
        hash = `trailersmall2`,
    },
    trash = {
        name = 'Trashmaster',
        brand = 'Jobuilt',
        model = 'trash',
        price = 59939,
        category = 'service',
        type = 'automobile',
        hash = `trash`,
    },
    trash2 = {
        name = 'Trashmaster (Heist)',
        brand = 'Jobuilt',
        model = 'trash2',
        price = 59939,
        category = 'service',
        type = 'automobile',
        hash = `trash2`,
    },
    trflat = {
        name = 'Trailer (Flatbed)',
        brand = '',
        model = 'trflat',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `trflat`,
    },
    tribike = {
        name = 'Whippet Race Bike',
        brand = '',
        model = 'tribike',
        price = 3622,
        category = 'cycles',
        type = 'bike',
        hash = `tribike`,
    },
    tribike2 = {
        name = 'Endurex Race Bike',
        brand = '',
        model = 'tribike2',
        price = 3622,
        category = 'cycles',
        type = 'bike',
        hash = `tribike2`,
    },
    tribike3 = {
        name = 'Tri-Cycles Race Bike',
        brand = '',
        model = 'tribike3',
        price = 3622,
        category = 'cycles',
        type = 'bike',
        hash = `tribike3`,
    },
    trophytruck = {
        name = 'Trophy Truck',
        brand = 'Vapid',
        model = 'trophytruck',
        price = 71559,
        category = 'offroad',
        type = 'automobile',
        hash = `trophytruck`,
    },
    trophytruck2 = {
        name = 'Desert Raid',
        brand = 'Vapid',
        model = 'trophytruck2',
        price = 71559,
        category = 'offroad',
        type = 'automobile',
        hash = `trophytruck2`,
    },
    tropic = {
        name = 'Tropic',
        brand = 'Shitzu',
        model = 'tropic',
        price = 390680,
        category = 'boats',
        type = 'boat',
        hash = `tropic`,
    },
    tropic2 = {
        name = 'Tropic (Yacht)',
        brand = 'Shitzu',
        model = 'tropic2',
        price = 390680,
        category = 'boats',
        type = 'boat',
        hash = `tropic2`,
    },
    tropos = {
        name = 'Tropos Rallye',
        brand = 'Lampadati',
        model = 'tropos',
        price = 71140,
        category = 'sports',
        type = 'automobile',
        hash = `tropos`,
    },
    tug = {
        name = 'Tug',
        brand = 'Buckingham',
        model = 'tug',
        price = 67079,
        category = 'boats',
        type = 'boat',
        hash = `tug`,
    },
    tula = {
        name = 'Tula',
        brand = 'Mammoth',
        model = 'tula',
        price = 1321065,
        category = 'planes',
        type = 'plane',
        hash = `tula`,
    },
    tulip = {
        name = 'Tulip',
        brand = 'Declasse',
        model = 'tulip',
        price = 80611,
        category = 'muscle',
        type = 'automobile',
        hash = `tulip`,
    },
    tulip2 = {
        name = 'Tulip M-100',
        brand = 'Declasse',
        model = 'tulip2',
        price = 74746,
        category = 'muscle',
        type = 'automobile',
        hash = `tulip2`,
    },
    turismo2 = {
        name = 'Turismo Classic',
        brand = 'Grotti',
        model = 'turismo2',
        price = 81835,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `turismo2`,
    },
    turismo3 = {
        name = 'Turismo Omaggio',
        brand = 'Grotti',
        model = 'turismo3',
        price = 83545,
        category = 'super',
        type = 'automobile',
        hash = `turismo3`,
    },
    turismor = {
        name = 'Grotti Turismo R',
        brand = 'Grotti',
        model = 'turismor',
        price = 85403,
        category = 'super',
        type = 'automobile',
        hash = `turismor`,
    },
    tvtrailer = {
        name = 'Trailer (Fame or Shame)',
        brand = '',
        model = 'tvtrailer',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `tvtrailer`,
    },
    tvtrailer2 = {
        name = 'Trailer',
        brand = '',
        model = 'tvtrailer2',
        price = 5668,
        category = 'utility',
        type = 'trailer',
        hash = `tvtrailer2`,
    },
    tyrant = {
        name = 'Tyrant',
        brand = 'Överflöd',
        model = 'tyrant',
        price = 88321,
        category = 'super',
        type = 'automobile',
        hash = `tyrant`,
    },
    tyrus = {
        name = 'Tyrus',
        brand = 'Progen',
        model = 'tyrus',
        price = 84350,
        category = 'super',
        type = 'automobile',
        hash = `tyrus`,
    },
    utillitruck = {
        name = 'Utility Truck (Cherry Picker)',
        brand = 'Vapid',
        model = 'utillitruck',
        price = 50131,
        category = 'utility',
        type = 'automobile',
        hash = `utillitruck`,
    },
    utillitruck2 = {
        name = 'Utility Truck (Cargo)',
        brand = 'Vapid',
        model = 'utillitruck2',
        price = 50131,
        category = 'utility',
        type = 'automobile',
        hash = `utillitruck2`,
    },
    utillitruck3 = {
        name = 'Utility Truck (Van)',
        brand = 'Vapid',
        model = 'utillitruck3',
        price = 50131,
        category = 'utility',
        type = 'automobile',
        hash = `utillitruck3`,
    },
    uranus = {
        name = 'Uranus LozSpeed',
        brand = 'Vapid',
        model = 'uranus',
        price = 50131,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `uranus`,
    },
    vacca = {
        name = 'Vacca',
        brand = 'Pegassi',
        model = 'vacca',
        price = 83666,
        category = 'super',
        type = 'automobile',
        hash = `vacca`,
    },
    vader = {
        name = 'Vader',
        brand = 'Shitzu',
        model = 'vader',
        price = 22163,
        category = 'motorcycles',
        type = 'bike',
        hash = `vader`,
    },
    vagner = {
        name = 'Vagner',
        brand = 'Dewbauchee',
        model = 'vagner',
        price = 87808,
        category = 'super',
        type = 'automobile',
        hash = `vagner`,
    },
    vagrant = {
        name = 'Vagrant',
        brand = 'Maxwell',
        model = 'vagrant',
        price = 77576,
        category = 'offroad',
        type = 'automobile',
        hash = `vagrant`,
    },
    valkyrie = {
        name = 'Valkyrie',
        brand = 'Buckingham',
        model = 'valkyrie',
        price = 6116097,
        category = 'helicopters',
        type = 'heli',
        hash = `valkyrie`,
    },
    valkyrie2 = {
        name = 'Valkyrie MOD.0',
        brand = 'Buckingham',
        model = 'valkyrie2',
        price = 6116097,
        category = 'helicopters',
        type = 'heli',
        hash = `valkyrie2`,
    },
    vamos = {
        name = 'Vamos',
        brand = 'Declasse',
        model = 'vamos',
        price = 75751,
        category = 'muscle',
        type = 'automobile',
        hash = `vamos`,
    },
    vectre = {
        name = 'Vectre',
        brand = 'Emperor',
        model = 'vectre',
        price = 73202,
        category = 'sports',
        type = 'automobile',
        hash = `vectre`,
    },
    velum = {
        name = 'Velum',
        brand = 'JoBuilt',
        model = 'velum',
        price = 1439638,
        category = 'planes',
        type = 'plane',
        hash = `velum`,
    },
    velum2 = {
        name = 'Velum',
        brand = 'JoBuilt',
        model = 'velum2',
        price = 1439638,
        category = 'planes',
        type = 'plane',
        hash = `velum2`,
    },
    verlierer2 = {
        name = 'Verlierer',
        brand = 'Bravado',
        model = 'verlierer2',
        price = 83280,
        category = 'sports',
        type = 'automobile',
        hash = `verlierer2`,
    },
    verus = {
        name = 'Verus',
        brand = 'Dinka',
        model = 'verus',
        price = 16526,
        category = 'offroad',
        type = 'automobile',
        hash = `verus`,
    },
    vestra = {
        name = 'Vestra',
        brand = 'Buckingham',
        model = 'vestra',
        price = 1982877,
        category = 'planes',
        type = 'plane',
        hash = `vestra`,
    },
    vetir = {
        name = 'Vetir',
        brand = 'HVY',
        model = 'vetir',
        price = 38828,
        category = 'military',
        type = 'automobile',
        hash = `vetir`,
    },
    veto = {
        name = 'Veto Classic',
        brand = 'Dinka',
        model = 'veto',
        price = 42608,
        category = 'sports',
        type = 'automobile',
        hash = `veto`,
    },
    veto2 = {
        name = 'Veto Modern',
        brand = 'Dinka',
        model = 'veto2',
        price = 45282,
        category = 'sports',
        type = 'automobile',
        hash = `veto2`,
    },
    vigero = {
        name = 'Vigero',
        brand = 'Declasse',
        model = 'vigero',
        price = 77482,
        category = 'muscle',
        type = 'automobile',
        hash = `vigero`,
    },
    vigero2 = {
        name = 'Vigero ZX',
        brand = 'Declasse',
        model = 'vigero2',
        price = 82143,
        category = 'muscle',
        type = 'automobile',
        hash = `vigero2`,
    },
    vigero3 = {
        name = 'Vigero ZX Convertible',
        brand = 'Declasse',
        model = 'vigero3',
        price = 81866,
        category = 'muscle',
        type = 'automobile',
        hash = `vigero3`,
    },
    vigilante = {
        name = 'Vigilante',
        brand = 'Grotti',
        model = 'vigilante',
        price = 261085,
        category = 'super',
        type = 'automobile',
        hash = `vigilante`,
    },
    vindicator = {
        name = 'Vindicator',
        brand = 'Dinka',
        model = 'vindicator',
        price = 31055,
        category = 'motorcycles',
        type = 'bike',
        hash = `vindicator`,
    },
    virgo = {
        name = 'Virgo',
        brand = 'Albany',
        model = 'virgo',
        price = 60554,
        category = 'muscle',
        type = 'automobile',
        hash = `virgo`,
    },
    virgo2 = {
        name = 'Virgo Custom Classic',
        brand = 'Dundreary',
        model = 'virgo2',
        price = 60758,
        category = 'muscle',
        type = 'automobile',
        hash = `virgo2`,
    },
    virgo3 = {
        name = 'Virgo Custom Classic',
        brand = 'Dundreary',
        model = 'virgo3',
        price = 60554,
        category = 'muscle',
        type = 'automobile',
        hash = `virgo3`,
    },
    virtue = {
        name = 'Virtue',
        brand = 'Ocelot',
        model = 'virtue',
        price = 76383,
        category = 'super',
        type = 'automobile',
        hash = `virtue`,
    },
    viseris = {
        name = 'Viseris',
        brand = 'Lampadati',
        model = 'viseris',
        price = 78246,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `viseris`,
    },
    visione = {
        name = 'Visione',
        brand = 'Grotti',
        model = 'visione',
        price = 85576,
        category = 'super',
        type = 'automobile',
        hash = `visione`,
    },
    vivanite = {
        name = 'Vivanite',
        brand = 'Karin',
        model = 'vivanite',
        price = 46334,
        category = 'suvs',
        type = 'automobile',
        hash = `vivanite`,
    },
    volatol = {
        name = 'Volatol',
        brand = '',
        model = 'volatol',
        price = 1570129,
        category = 'planes',
        type = 'plane',
        hash = `volatol`,
    },
    volatus = {
        name = 'Volatus',
        brand = 'Buckingham',
        model = 'volatus',
        price = 6589212,
        category = 'helicopters',
        type = 'heli',
        hash = `volatus`,
    },
    voltic = {
        name = 'Voltic',
        brand = 'Coil',
        model = 'voltic',
        price = 60326,
        category = 'super',
        type = 'automobile',
        hash = `voltic`,
    },
    voltic2 = {
        name = 'Rocket Voltic',
        brand = 'Coil',
        model = 'voltic2',
        price = 180978,
        category = 'super',
        type = 'automobile',
        hash = `voltic2`,
    },
    voodoo = {
        name = 'Voodoo Custom',
        brand = 'Declasse',
        model = 'voodoo',
        price = 66162,
        category = 'muscle',
        type = 'automobile',
        hash = `voodoo`,
    },
    voodoo2 = {
        name = 'Voodoo',
        brand = 'Declasse',
        model = 'voodoo2',
        price = 63070,
        category = 'muscle',
        type = 'automobile',
        hash = `voodoo2`,
    },
    vorschlaghammer = {
        name = 'Vorschlaghammer',
        brand = 'Benefactor',
        model = 'vorschlaghammer',
        price = 25612,
        category = 'sedans',
        type = 'automobile',
        hash = `vorschlaghammer`,
    },
    vortex = {
        name = 'Vortex',
        brand = 'Pegassi',
        model = 'vortex',
        price = 25612,
        category = 'motorcycles',
        type = 'bike',
        hash = `vortex`,
    },
    vstr = {
        name = 'V-STR',
        brand = 'Albany',
        model = 'vstr',
        price = 79649,
        category = 'sports',
        type = 'automobile',
        hash = `vstr`,
    },
    warrener = {
        name = 'Warrener',
        brand = 'Vulcar',
        model = 'warrener',
        price = 64367,
        category = 'sedans',
        type = 'automobile',
        hash = `warrener`,
    },
    warrener2 = {
        name = 'Warrener HKR',
        brand = 'Vulcar',
        model = 'warrener2',
        price = 67374,
        category = 'sedans',
        type = 'automobile',
        hash = `warrener2`,
    },
    washington = {
        name = 'Washington',
        brand = 'Albany',
        model = 'washington',
        price = 71053,
        category = 'sedans',
        type = 'automobile',
        hash = `washington`,
    },
    wastelander = {
        name = 'Wastelander',
        brand = 'MTL',
        model = 'wastelander',
        price = 67837,
        category = 'service',
        type = 'automobile',
        hash = `wastelander`,
    },
    weevil = {
        name = 'Weevil',
        brand = 'BF',
        model = 'weevil',
        price = 53983,
        category = 'compacts',
        type = 'automobile',
        hash = `weevil`,
    },
    weevil2 = {
        name = 'Weevil Custom',
        brand = 'BF',
        model = 'weevil2',
        price = 83800,
        category = 'muscle',
        type = 'automobile',
        hash = `weevil2`,
    },
    windsor = {
        name = 'Windsor',
        brand = 'Enus',
        model = 'windsor',
        price = 79422,
        category = 'coupes',
        type = 'automobile',
        hash = `windsor`,
    },
    windsor2 = {
        name = 'Windsor Drop',
        brand = 'Enus',
        model = 'windsor2',
        price = 79252,
        category = 'coupes',
        type = 'automobile',
        hash = `windsor2`,
    },
    winky = {
        name = 'Winky',
        brand = 'Vapid',
        model = 'winky',
        price = 49504,
        category = 'offroad',
        type = 'automobile',
        hash = `winky`,
    },
    wolfsbane = {
        name = 'Wolfsbane',
        brand = 'Western',
        model = 'wolfsbane',
        price = 19441,
        category = 'motorcycles',
        type = 'bike',
        hash = `wolfsbane`,
    },
    xa21 = {
        name = 'XA-21',
        brand = 'Ocelot',
        model = 'xa21',
        price = 82892,
        category = 'super',
        type = 'automobile',
        hash = `xa21`,
    },
    xls = {
        name = 'XLS',
        brand = 'Benefactor',
        model = 'xls',
        price = 72768,
        category = 'suvs',
        type = 'automobile',
        hash = `xls`,
    },
    xls2 = {
        name = 'XLS (Armored)',
        brand = 'Benefactor',
        model = 'xls2',
        price = 72800,
        category = 'suvs',
        type = 'automobile',
        hash = `xls2`,
    },
    yosemite = {
        name = 'Yosemite',
        brand = 'Declasse',
        model = 'yosemite',
        price = 69338,
        category = 'muscle',
        type = 'automobile',
        hash = `yosemite`,
    },
    yosemite2 = {
        name = 'Yosemite Drift',
        brand = 'Declasse',
        model = 'yosemite2',
        price = 74171,
        category = 'muscle',
        type = 'automobile',
        hash = `yosemite2`,
    },
    yosemite3 = {
        name = 'Yosemite Rancher',
        brand = 'Declasse',
        model = 'yosemite3',
        price = 66896,
        category = 'offroad',
        type = 'automobile',
        hash = `yosemite3`,
    },
    yosemite1500 = {
        name = 'Yosemite 1500',
        brand = 'Declasse',
        model = 'yosemite1500',
        price = 66896,
        category = 'offroad',
        type = 'automobile',
        hash = `yosemite1500`,
    },
    youga = {
        name = 'Youga',
        brand = 'Bravado',
        model = 'youga',
        price = 55630,
        category = 'vans',
        type = 'automobile',
        hash = `youga`,
    },
    youga2 = {
        name = 'Youga Classic',
        brand = 'Bravado',
        model = 'youga2',
        price = 55630,
        category = 'vans',
        type = 'automobile',
        hash = `youga2`,
    },
    youga3 = {
        name = 'Youga Classic 4x4',
        brand = 'Bravado',
        model = 'youga3',
        price = 63150,
        category = 'vans',
        type = 'automobile',
        hash = `youga3`,
    },
    youga4 = {
        name = 'Youga Custom',
        brand = 'Vapid',
        model = 'youga4',
        price = 60252,
        category = 'vans',
        type = 'automobile',
        hash = `youga4`,
    },
    youga5 = {
        name = 'Youga Custom',
        brand = 'Vapid',
        model = 'youga5',
        price = 60252,
        category = 'vans',
        type = 'automobile',
        hash = `youga5`,
    },
    z190 = {
        name = '190Z',
        brand = 'Karin',
        model = 'z190',
        price = 73299,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `z190`,
    },
    zeno = {
        name = 'Zeno',
        brand = 'Överflöd',
        model = 'zeno',
        price = 89601,
        category = 'super',
        type = 'automobile',
        hash = `zeno`,
    },
    zentorno = {
        name = 'Zentorno',
        brand = 'Pegassi',
        model = 'zentorno',
        price = 82795,
        category = 'super',
        type = 'automobile',
        hash = `zentorno`,
    },
    zhaba = {
        name = 'Zhaba',
        brand = 'RUNE',
        model = 'zhaba',
        price = 50950,
        category = 'offroad',
        type = 'automobile',
        hash = `zhaba`,
    },
    zion = {
        name = 'Zion',
        brand = 'Übermacht',
        model = 'zion',
        price = 77752,
        category = 'coupes',
        type = 'automobile',
        hash = `zion`,
    },
    zion2 = {
        name = 'Zion Cabrio',
        brand = 'Übermacht',
        model = 'zion2',
        price = 77752,
        category = 'coupes',
        type = 'automobile',
        hash = `zion2`,
    },
    zion3 = {
        name = 'Zion Classic',
        brand = 'Übermacht',
        model = 'zion3',
        price = 74281,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `zion3`,
    },
    zombiea = {
        name = 'Zombie Bobber',
        brand = 'Western',
        model = 'zombiea',
        price = 23182,
        category = 'motorcycles',
        type = 'bike',
        hash = `zombiea`,
    },
    zombieb = {
        name = 'Zombie Chopper',
        brand = 'Western',
        model = 'zombieb',
        price = 23182,
        category = 'motorcycles',
        type = 'bike',
        hash = `zombieb`,
    },
    zorrusso = {
        name = 'Pegassi Zorrusso',
        brand = 'Pegassi',
        model = 'zorrusso',
        price = 85159,
        category = 'super',
        type = 'automobile',
        hash = `zorrusso`,
    },
    zr350 = {
        name = 'ZR350',
        brand = 'Annis',
        model = 'zr350',
        price = 79974,
        category = 'sports',
        type = 'automobile',
        hash = `zr350`,
    },
    zr380 = {
        name = 'Apocalypse ZR380',
        brand = 'Annis',
        model = 'zr380',
        price = 83632,
        category = 'sports',
        type = 'automobile',
        hash = `zr380`,
    },
    zr3802 = {
        name = 'Future Shock ZR380',
        brand = 'Annis',
        model = 'zr3802',
        price = 83632,
        category = 'sports',
        type = 'automobile',
        hash = `zr3802`,
    },
    zr3803 = {
        name = 'Nightmare ZR380',
        brand = 'Annis',
        model = 'zr3803',
        price = 83632,
        category = 'sports',
        type = 'automobile',
        hash = `zr3803`,
    },
    ztype = {
        name = 'Z-Type',
        brand = 'Truffade',
        model = 'ztype',
        price = 84821,
        category = 'sportsclassics',
        type = 'automobile',
        hash = `ztype`,
    }
}