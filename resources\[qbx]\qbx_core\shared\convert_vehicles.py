import re
import os

def clean_text(text):
    """Odstraní všechny nepovolené zna<PERSON> (apostrofy, zá<PERSON>ky, speciální znaky)"""
    return re.sub(r"[^a-zA-Z0-9 ]", "", text)

def parse_vehicles_lua(file_path):
    vehicles = []
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        
        pattern = re.compile(r'(\w+)\s*=\s*{([^}]+)}', re.DOTALL)
        matches = pattern.finditer(content)
        
        for match in matches:
            vehicle_id = match.group(1)
            vehicle_data = match.group(2)
            
            props = {}
            prop_pattern = re.compile(r'(\w+)\s*=\s*(?:`([^`]+)`|([^,\n]+))')
            for prop_match in prop_pattern.finditer(vehicle_data):
                prop_name = prop_match.group(1)
                prop_value = prop_match.group(2) or prop_match.group(3)
                props[prop_name.strip()] = prop_value.strip().strip('"\'')
            
            if props:
                img_url = f"https://vehicles.diverserp.cz/src/veh_images/{props.get('model', '')}.png"
                
                vehicles.append({
                    'id': vehicle_id,
                    'name': clean_text(props.get('name', '')),  # Vyčištěný název
                    'brand': clean_text(props.get('brand', '')),
                    'model': props.get('model', ''),
                    'price': props.get('price', 0),
                    'category': props.get('category', ''),
                    'type': props.get('type', ''),
                    'hash': props.get('hash', ''),
                    'img_url': img_url
                })
    
    return vehicles

def generate_sql(vehicles, output_file):
    with open(output_file, 'w', encoding='utf-8') as file:
        file.write("-- Auto-generated SQL file from vehicles.lua\n")
        file.write("-- Inserts vehicles into core_vehicles table\n\n")
        
        file.write("USE `Qbox_DVRP-2_0`;\n\n")
        
        for vehicle in vehicles:
            file.write(
                f"INSERT INTO `core_vehicles` SET `id` = '{vehicle['id']}', `name` = '{vehicle['name']}', "
                f"`brand` = '{vehicle['brand']}', `model` = '{vehicle['model']}', `price` = {vehicle['price']}, "
                f"`category` = '{vehicle['category']}', `type` = '{vehicle['type']}', "
                f"`hash` = '{vehicle['hash']}', `img_url` = '{vehicle['img_url']}';\n"
            )

def main():
    input_file = 'vehicles.lua'
    output_file = 'vehicles.sql'
    
    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found in the current directory.")
        return
    
    vehicles = parse_vehicles_lua(input_file)
    
    if not vehicles:
        print("No vehicles found in the file.")
        return
    
    generate_sql(vehicles, output_file)
    print(f"Successfully generated {output_file} with {len(vehicles)} vehicles.")

if __name__ == '__main__':
    main()