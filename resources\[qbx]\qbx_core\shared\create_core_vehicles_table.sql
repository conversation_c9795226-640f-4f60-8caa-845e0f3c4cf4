-- Create core_vehicles table for shared vehicle data
-- This table stores all vehicle information that was previously in vehicles.lua

USE `Qbox_DVRP-2_0`;

CREATE TABLE IF NOT EXISTS `core_vehicles` (
    `id` VARCHAR(50) NOT NULL PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `brand` VARCHAR(50) NOT NULL,
    `model` VARCHAR(50) NOT NULL,
    `price` INT(11) NOT NULL DEFAULT 0,
    `category` VARCHAR(50) NOT NULL,
    `type` VARCHAR(20) NOT NULL,
    `hash` VARCHAR(50) NOT NULL,
    `img_url` VARCHAR(255) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_type` (`type`),
    INDEX `idx_hash` (`hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
