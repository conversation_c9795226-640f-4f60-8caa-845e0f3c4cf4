-- Vehicles loader - handles loading vehicles from database on server and syncing to clients
-- This is separate from shared.main to avoid circular dependencies

local VehiclesLoader = {}

-- Initialize vehicles data
VehiclesLoader.Vehicles = {}
VehiclesLoader.VehicleHashes = {}

-- Load vehicles from database (server side only)
function VehiclesLoader.LoadVehicles()
    if not IsDuplicityVersion() then
        return VehiclesLoader.Vehicles
    end

    -- Server side - load from database
    local result = MySQL.Sync.fetchAll('SELECT * FROM core_vehicles', {})

    if result then
        for _, vehicle in pairs(result) do
            VehiclesLoader.Vehicles[vehicle.id] = {
                name = vehicle.name,
                brand = vehicle.brand,
                model = vehicle.model,
                price = vehicle.price,
                category = vehicle.category,
                type = vehicle.type,
                hash = joaat(vehicle.hash), -- Convert hash string to joaat hash
            }
        end
    end

    -- Build vehicle hashes table
    for _, v in pairs(VehiclesLoader.Vehicles) do
        VehiclesLoader.VehicleHashes[v.hash] = v
    end

    local count = 0
    for _ in pairs(VehiclesLoader.Vehicles) do count = count + 1 end
    print(('[qbx_core] Loaded %d vehicles from database'):format(count))

    return VehiclesLoader.Vehicles
end

-- Update vehicles data (used by client when receiving from server)
function VehiclesLoader.UpdateVehicles(vehicles)
    VehiclesLoader.Vehicles = vehicles
    
    -- Rebuild vehicle hashes table
    VehiclesLoader.VehicleHashes = {}
    for _, v in pairs(VehiclesLoader.Vehicles) do
        VehiclesLoader.VehicleHashes[v.hash] = v
    end
end

return VehiclesLoader
