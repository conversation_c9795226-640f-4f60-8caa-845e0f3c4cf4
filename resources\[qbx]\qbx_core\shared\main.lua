local qbShared = {}
qbShared.ForceJobDefaultDutyAtLogin = true -- true: Force duty state to jobdefaultDuty | false: set duty state from database last saved
qbShared.Locations = require 'shared.locations'
qbShared.Weapons = require 'shared.weapons'

-- Vehicles will be loaded separately in server/client main.lua to avoid circular dependency
qbShared.Vehicles = {}
qbShared.VehicleHashes = {}

return qbShared
