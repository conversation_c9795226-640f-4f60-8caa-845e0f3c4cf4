local qbShared = {}
qbShared.ForceJobDefaultDutyAtLogin = true -- true: Force duty state to jobdefaultDuty | false: set duty state from database last saved
qbShared.Locations = require 'shared.locations'
qbShared.Weapons = require 'shared.weapons'

-- Load vehicles from database
if IsDuplicityVersion() then
    -- Server side - load directly from vehicles.lua (which loads from database)
    qbShared.Vehicles = require 'shared.vehicles'
else
    -- Client side - request from server and wait for response
    qbShared.Vehicles = {}

    local vehiclesLoaded = false
    local promise = promise.new()

    -- Request vehicles from server
    TriggerServerEvent('qbx_core:server:requestVehicles')

    -- Wait for server response
    RegisterNetEvent('qbx_core:client:receiveVehicles', function(serverVehicles)
        qbShared.Vehicles = serverVehicles
        vehiclesLoaded = true
        promise:resolve()
    end)

    -- Wait for vehicles to be loaded
    Citizen.Await(promise)
end

---@type table<number, Vehicle>
qbShared.VehicleHashes = {}

-- Build vehicle hashes table
for _, v in pairs(qbShared.Vehicles) do
    qbShared.VehicleHashes[v.hash] = v
end

return qbShared
