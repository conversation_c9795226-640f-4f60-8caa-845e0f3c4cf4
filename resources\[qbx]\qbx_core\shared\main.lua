local qbShared = {}
qbShared.ForceJobDefaultDutyAtLogin = true -- true: Force duty state to jobdefaultDuty | false: set duty state from database last saved
qbShared.Locations = require 'shared.locations'
qbShared.Weapons = require 'shared.weapons'

-- Initialize vehicles table
qbShared.Vehicles = {}
qbShared.VehicleHashes = {}

-- Load vehicles from database (server side only)
if IsDuplicityVersion() then
    qbShared.Vehicles = require 'shared.vehicles'

    -- Build vehicle hashes table
    for _, v in pairs(qbShared.Vehicles) do
        qbShared.VehicleHashes[v.hash] = v
    end
end

return qbShared
